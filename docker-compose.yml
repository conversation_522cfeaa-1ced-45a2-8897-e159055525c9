version: '3.8'

services:
  # 开发环境服务
  itmp-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "9999:9999"
    volumes:
      # 挂载源代码以支持热重载
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    networks:
      - itmp-network
    restart: unless-stopped

  # 生产环境服务
  itmp-frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    networks:
      - itmp-network
    restart: unless-stopped
    profiles:
      - production

networks:
  itmp-network:
    driver: bridge
