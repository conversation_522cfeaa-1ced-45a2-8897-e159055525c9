# 多阶段构建 - 构建阶段
FROM node:18-alpine as build-stage

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_OPTIONS=--max-old-space-size=8192

# 设置 npm 镜像源（可选，提高国内下载速度）
RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm
RUN npm install -g pnpm@8.10.0

# 复制 package.json 和相关配置文件
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml ./
COPY packages/ ./packages/
COPY internal/ ./internal/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 生产阶段 - 使用 Node.js 运行静态文件服务器
FROM node:18-alpine as production-stage

# 安装 serve 用于提供静态文件服务
RUN npm install -g serve

# 设置工作目录
WORKDIR /app

# 从构建阶段复制构建产物
COPY --from=build-stage /app/dist ./dist

# 暴露端口
EXPOSE 3000

# 启动静态文件服务器
CMD ["serve", "-s", "dist", "-l", "3000"]
