# 使用 Node.js 18 Alpine 作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=development
ENV NODE_OPTIONS=--max-old-space-size=8192

# 设置 npm 镜像源（可选，提高国内下载速度）
RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm
RUN npm install -g pnpm@8.10.0

# 暴露端口（默认 Vite 开发服务器端口）
EXPOSE 9999

# 启动命令 - 使用开发服务器
# 注意：这个版本需要通过卷挂载来提供源代码和依赖
CMD ["sh", "-c", "if [ ! -d node_modules ]; then echo '正在安装依赖...' && pnpm install --ignore-scripts; fi && echo '启动开发服务器...' && pnpm dev"]
