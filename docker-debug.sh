#!/bin/bash

echo "🔍 Docker 调试脚本"
echo "=================="

# 检查 Docker 是否运行
echo "1. 检查 Docker 状态..."
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请启动 Docker Desktop"
    echo "💡 在 macOS 上，请打开 Docker Desktop 应用"
    exit 1
else
    echo "✅ Docker 正在运行"
fi

# 停止现有容器
echo "2. 停止现有容器..."
docker-compose down 2>/dev/null || true

# 构建镜像
echo "3. 构建 Docker 镜像..."
docker-compose build itmp-frontend-dev

# 启动容器
echo "4. 启动开发容器..."
docker-compose up -d itmp-frontend-dev

# 等待容器启动
echo "5. 等待容器启动..."
sleep 5

# 检查容器状态
echo "6. 检查容器状态..."
docker-compose ps

# 检查容器日志
echo "7. 显示容器日志..."
docker-compose logs itmp-frontend-dev

# 检查端口
echo "8. 检查端口绑定..."
docker port $(docker-compose ps -q itmp-frontend-dev) 2>/dev/null || echo "无法获取端口信息"

echo ""
echo "🌐 如果一切正常，请访问: http://localhost:9999"
echo "📋 查看实时日志: docker-compose logs -f itmp-frontend-dev"
echo "🛑 停止服务: docker-compose down"
