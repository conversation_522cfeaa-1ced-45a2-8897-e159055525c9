import type { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';
import { isDevMode } from '@/utils/env';

// 测试路由配置 - 只在开发环境显示
const test: AppRouteModule = {
  path: '/test',
  name: 'Test',
  component: LAYOUT,
  redirect: '/test/chat-bubble',
  meta: {
    orderNo: 999,
    icon: 'ion:flask-outline',
    title: '测试页面',
    // 只在开发环境显示
    hideMenu: !isDevMode(),
  },
  children: [
    {
      path: 'chat-bubble',
      name: 'ChatBubbleTest',
      meta: {
        title: 'Dify 聊天气泡测试',
        ignoreKeepAlive: false,
        // 只在开发环境显示
        hideMenu: !isDevMode(),
      },
      component: () => import('@/views/test/ChatBubbleTest.vue'),
    },
    {
      path: 'fault-analysis-chat',
      name: 'FaultAnalysisChatTest',
      meta: {
        title: '故障分析聊天组件测试',
        ignoreKeepAlive: false,
        // 只在开发环境显示
        hideMenu: !isDevMode(),
      },
      component: () => import('@/views/test/chat-test.vue'),
    },
    {
      path: 'fault-analysis-chat-markdown',
      name: 'FaultAnalysisChatMarkdownTest',
      meta: {
        title: '故障分析聊天 Markdown 渲染测试',
        ignoreKeepAlive: false,
        // 只在开发环境显示
        hideMenu: !isDevMode(),
      },
      component: () => import('@/views/test/fault-analysis-chat-markdown.vue'),
    },
    {
      path: 'markdown-table-test',
      name: 'MarkdownTableTest',
      meta: {
        title: 'Markdown 表格渲染测试',
        ignoreKeepAlive: false,
        // 只在开发环境显示
        hideMenu: !isDevMode(),
      },
      component: () => import('@/pages/test/markdown-table-test.vue'),
    },
  ],
};

// 只在开发环境导出测试路由，生产环境返回 null
export default isDevMode() ? test : null;
