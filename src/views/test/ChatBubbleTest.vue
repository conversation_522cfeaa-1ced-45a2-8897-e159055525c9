<template>
  <PageWrapper title="聊天气泡测试页面">
    <div class="p-4">
      <Card title="聊天气泡测试页面">
        <div class="text-center py-8">
          <div class="text-lg text-gray-600 mb-4"> ⚠️ 聊天气泡功能已移除 </div>
          <p class="text-gray-500 mb-4"> 根据业务需求，聊天机器人气泡现在只在故障详情页展示。 </p>
          <p class="text-gray-500"> 如需测试聊天功能，请访问任意故障详情页面。 </p>
        </div>
      </Card>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { PageWrapper } from '@/components/Page';
  import { Card } from 'ant-design-vue';

  defineOptions({ name: 'ChatBubbleTest' });
</script>
