<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">故障分析聊天 - Markdown 渲染测试</h1>

    <div class="mb-4">
      <a-button @click="openChat" type="primary">打开聊天窗口</a-button>
      <a-button @click="addMarkdownMessage" class="ml-2">添加完整报告</a-button>
      <a-button @click="clearMessages" class="ml-2">清空消息</a-button>
    </div>

    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">快速测试：</h3>
      <a-button @click="testCodeHighlight" size="small">代码高亮测试</a-button>
      <a-button @click="testTable" size="small">表格测试</a-button>
      <a-button @click="testList" size="small">列表测试</a-button>
      <a-button @click="testQuote" size="small">引用测试</a-button>
      <a-button @click="testMixed" size="small">混合内容测试</a-button>
      <a-button @click="testLoading" size="small">Loading 状态测试</a-button>
      <a-button @click="testPureLoading" size="small">纯 Loading 测试</a-button>
      <a-button @click="toggleDirectLoading" size="small">切换直接测试 Loading</a-button>
      <a-button @click="testWorkflowEvents" size="small">测试工作流事件</a-button>
      <a-button @click="testSimpleLoading" size="small">最简单 Loading 测试</a-button>
      <a-button @click="testThinkingText" size="small">测试文字提示</a-button>
      <a-button @click="testDuplicateContent" size="small">测试重复内容修复</a-button>
    </div>

    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">调试信息：</h3>
      <div class="p-2 bg-gray-100 rounded text-sm">
        <div>消息数量: {{ chatRef?.messages?.length || 0 }}</div>
        <div v-if="chatRef?.messages?.length">
          最后一条消息:
          {{ JSON.stringify(chatRef.messages[chatRef.messages.length - 1], null, 2) }}
        </div>
        <div class="mt-2"> 直接测试项目: {{ JSON.stringify(directTestItems, null, 2) }} </div>
      </div>
    </div>

    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">测试用 Markdown 内容：</h3>
      <a-textarea
        v-model:value="testMarkdown"
        :rows="10"
        placeholder="输入 Markdown 内容进行测试..."
      />
      <a-button @click="sendTestMarkdown" class="mt-2" type="primary">发送测试内容</a-button>
    </div>

    <!-- 直接测试 BubbleList -->
    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-2">直接 BubbleList 测试：</h3>
      <div class="border p-4 rounded" style="height: 200px">
        <BubbleList
          :items="directTestItems"
          :roles="directTestRoles"
          :loading="bubbleListLoading"
        />
      </div>
      <div class="mt-2">
        <a-button @click="bubbleListLoading = !bubbleListLoading" size="small">
          切换 BubbleList Loading: {{ bubbleListLoading ? '开启' : '关闭' }}
        </a-button>
      </div>
    </div>

    <!-- 故障分析聊天组件 -->
    <FaultAnalysisChat
      ref="chatRef"
      :fault-id="'test-fault-001'"
      :fault-data="{ faultDescribe: '测试故障描述' }"
      :position="{ bottom: '2rem', right: '2rem' }"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, h } from 'vue';
  import FaultAnalysisChat from '@/components/FaultAnalysisChat/index.vue';
  import { BubbleList } from 'ant-design-x-vue';
  import Icon from '@/components/Icon/Icon.vue';

  const chatRef = ref();
  const bubbleListLoading = ref(false);

  // 直接测试 BubbleList 的数据
  const directTestItems = ref([
    {
      key: 'normal',
      role: 'ai',
      content: '这是正常消息',
    },
    {
      key: 'loading',
      role: 'ai',
      loading: true,
      content: '', // 确保有 content 字段
    },
  ]);

  const directTestRoles = ref({
    ai: {
      placement: 'start',
      avatar: {
        icon: h(Icon, { icon: 'ant-design:robot-outlined' }),
        style: { background: '#1677ff' },
      },
      loadingRender: () => h('a-spin', { size: 'small', tip: '加载中...' }),
      // 不使用 messageRender，让组件使用默认渲染
    },
  });

  const testMarkdown = ref(`# 🔧 故障分析报告

## 📋 问题概述

这是一个 **重要** 的故障分析报告，包含以下内容：

### 🚨 故障现象
- 系统响应缓慢
- 内存使用率过高
- 数据库连接超时

### 🔍 分析结果

> 经过详细分析，发现问题主要集中在以下几个方面：

1. **数据库性能问题**
   - 查询语句未优化
   - 索引缺失
   - 连接池配置不当

2. **内存泄漏**
   - 对象未及时释放
   - 缓存策略不当

3. **网络延迟**
   - 带宽不足
   - 路由配置问题

### 💡 解决方案

#### SQL 查询优化

\`\`\`sql
-- 优化查询语句
SELECT u.id, u.username, u.email, u.status
FROM users u
WHERE u.status = 'active'
  AND u.created_at > '2024-01-01'
  AND u.department_id IN (1, 2, 3)
ORDER BY u.last_login_at DESC
LIMIT 100;

-- 添加索引
CREATE INDEX idx_users_status_created ON users(status, created_at);
CREATE INDEX idx_users_department ON users(department_id);
\`\`\`

#### JavaScript 内存管理

\`\`\`javascript
// 内存管理优化
class ResourceManager {
  constructor() {
    this.cache = new Map();
    this.timers = new Set();
  }

  // 清理资源
  cleanup() {
    // 清理缓存
    this.cache.clear();

    // 清理定时器
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();

    // 移除事件监听器
    this.removeEventListeners();
  }

  // 内存监控
  monitorMemory() {
    if (performance.memory) {
      const { usedJSHeapSize, totalJSHeapSize } = performance.memory;
      const usage = (usedJSHeapSize / totalJSHeapSize) * 100;

      if (usage > 80) {
        console.warn('内存使用率过高:', usage.toFixed(2) + '%');
        this.cleanup();
      }
    }
  }
}
\`\`\`

#### Python 性能优化

\`\`\`python
import asyncio
import aiohttp
from typing import List, Dict
import logging

class PerformanceOptimizer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def batch_process(self, items: List[Dict]) -> List[Dict]:
        """批量处理数据以提高性能"""
        tasks = []
        for item in items:
            task = asyncio.create_task(self.process_item(item))
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        return [r for r in results if not isinstance(r, Exception)]

    async def process_item(self, item: Dict) -> Dict:
        """处理单个项目"""
        try:
            # 模拟异步处理
            await asyncio.sleep(0.1)
            return {"id": item["id"], "status": "processed"}
        except Exception as e:
            self.logger.error(f"处理项目失败: {e}")
            raise
\`\`\`

### 📊 监控指标

| 指标 | 故障前 | 故障后 | 目标值 | 状态 |
|------|--------|--------|--------|------|
| 响应时间 | 2000ms | 500ms | <200ms | 🟡 改善中 |
| 内存使用 | 85% | 60% | <70% | ✅ 达标 |
| CPU使用率 | 90% | 45% | <60% | ✅ 达标 |
| 数据库连接 | 95% | 70% | <80% | ✅ 达标 |

### 🛡️ 预防措施

- [x] 建立监控告警系统
- [x] 定期性能测试
- [x] 代码审查流程
- [ ] 容量规划
- [ ] 故障演练
- [ ] 自动化部署

### 📈 配置示例

#### Nginx 配置优化

\`\`\`nginx
server {
    listen 80;
    server_name example.com;

    # 启用 gzip 压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript;

    # 设置缓存
    location ~* \\.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 代理设置
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_connect_timeout 30s;
        proxy_read_timeout 30s;
    }
}
\`\`\`

#### Docker 配置

\`\`\`dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:3000/health || exit 1

# 启动应用
CMD ["npm", "start"]
\`\`\`

---

### ⚠️ 重要提醒

**注意**: 请定期检查系统状态，确保问题不再复现。

📚 更多信息请参考：
- [系统监控文档](https://example.com/docs/monitoring)
- [性能优化指南](https://example.com/docs/performance)
- [故障处理流程](https://example.com/docs/incident-response)

---

*报告生成时间: ${new Date().toLocaleString()}*
`);

  function openChat() {
    if (chatRef.value) {
      chatRef.value.openChat();
    }
  }

  function addMarkdownMessage() {
    if (chatRef.value && chatRef.value.messages) {
      chatRef.value.messages.push({
        type: 'bot',
        content: testMarkdown.value,
        timestamp: Date.now(),
      });
    }
  }

  function sendTestMarkdown() {
    if (chatRef.value && chatRef.value.messages) {
      // 添加用户消息
      chatRef.value.messages.push({
        type: 'user',
        content: '请分析这个故障',
        timestamp: Date.now(),
      });

      // 添加 AI 回复（包含 markdown）
      setTimeout(() => {
        chatRef.value.messages.push({
          type: 'bot',
          content: testMarkdown.value,
          timestamp: Date.now(),
        });
      }, 500);
    }
  }

  function clearMessages() {
    if (chatRef.value && chatRef.value.messages) {
      chatRef.value.messages.splice(0);
    }
  }

  // 测试代码高亮
  function testCodeHighlight() {
    const codeContent = `## 🔧 代码高亮测试

### JavaScript 示例
\`\`\`javascript
const fetchData = async (url) => {
  try {
    const response = await fetch(url);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取数据失败:', error);
    throw error;
  }
};
\`\`\`

### Python 示例
\`\`\`python
import asyncio
import aiohttp

async def fetch_data(session, url):
    async with session.get(url) as response:
        return await response.json()

async def main():
    async with aiohttp.ClientSession() as session:
        data = await fetch_data(session, 'https://api.example.com/data')
        print(data)
\`\`\`

### SQL 示例
\`\`\`sql
SELECT
    u.id,
    u.username,
    COUNT(o.id) as order_count,
    SUM(o.total_amount) as total_spent
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.created_at >= '2024-01-01'
GROUP BY u.id, u.username
HAVING COUNT(o.id) > 5
ORDER BY total_spent DESC;
\`\`\``;

    addBotMessage(codeContent);
  }

  // 测试表格
  function testTable() {
    const tableContent = `## 📊 表格测试

### 系统性能监控

| 服务名称 | CPU使用率 | 内存使用率 | 磁盘使用率 | 状态 | 最后检查时间 |
|----------|-----------|------------|------------|------|--------------|
| Web服务器 | 45% | 62% | 78% | ✅ 正常 | 2024-01-15 10:30 |
| 数据库服务 | 78% | 85% | 45% | ⚠️ 警告 | 2024-01-15 10:29 |
| 缓存服务 | 23% | 34% | 12% | ✅ 正常 | 2024-01-15 10:31 |
| 消息队列 | 56% | 67% | 23% | ✅ 正常 | 2024-01-15 10:28 |

### 错误统计

| 错误类型 | 今日次数 | 昨日次数 | 变化趋势 |
|----------|----------|----------|----------|
| 连接超时 | 12 | 8 | 📈 +50% |
| 内存溢出 | 3 | 5 | 📉 -40% |
| 权限错误 | 0 | 2 | 📉 -100% |
| 数据库锁 | 7 | 12 | 📉 -42% |`;

    addBotMessage(tableContent);
  }

  // 测试列表
  function testList() {
    const listContent = `## 📝 列表测试

### 有序列表 - 故障处理步骤

1. **问题识别**
   1. 收集错误日志
   2. 分析用户反馈
   3. 监控系统指标

2. **问题分析**
   1. 确定影响范围
   2. 分析根本原因
   3. 评估修复方案

3. **问题解决**
   1. 实施修复方案
   2. 验证修复效果
   3. 更新文档记录

### 无序列表 - 检查清单

- **系统检查**
  - ✅ 服务器状态正常
  - ✅ 数据库连接正常
  - ⚠️ 内存使用率偏高
  - ❌ 磁盘空间不足

- **网络检查**
  - ✅ 内网连通性正常
  - ✅ 外网访问正常
  - ✅ DNS解析正常

- **应用检查**
  - ✅ 应用服务运行中
  - ⚠️ 响应时间较慢
  - ✅ 日志记录正常`;

    addBotMessage(listContent);
  }

  // 测试引用
  function testQuote() {
    const quoteContent = `## 💬 引用测试

### 重要提醒

> ⚠️ **警告**: 在执行数据库操作前，请务必备份重要数据！

> 💡 **提示**: 建议在低峰期进行系统维护，以减少对用户的影响。

### 最佳实践

> **代码审查原则**
>
> 1. 每个 PR 至少需要两人审查
> 2. 关注代码的可读性和可维护性
> 3. 确保测试覆盖率达到 80% 以上
> 4. 检查是否有安全漏洞

> **部署流程**
>
> - 开发环境测试 ✅
> - 预发布环境验证 ✅
> - 生产环境部署 🔄
> - 监控和回滚准备 ⏳

### 引用中的代码

> 以下是推荐的错误处理方式：
>
> \`\`\`javascript
> try {
>   const result = await riskyOperation();
>   return result;
> } catch (error) {
>   logger.error('操作失败:', error);
>   throw new CustomError('操作失败，请稍后重试');
> }
> \`\`\``;

    addBotMessage(quoteContent);
  }

  // 测试混合内容
  function testMixed() {
    const mixedContent = `## 🎯 综合测试

### 故障报告 #2024-001

**报告时间**: ${new Date().toLocaleString()}
**严重级别**: 🔴 高
**影响范围**: 全站用户

---

#### 问题描述

> 用户反馈登录功能异常，出现以下错误信息：
>
> \`Error: Connection timeout after 30 seconds\`

#### 技术分析

1. **网络层面**
   - [x] CDN 状态正常
   - [x] 负载均衡器正常
   - [ ] 数据库连接池状态待检查

2. **应用层面**
   - 错误日志显示大量超时
   - 内存使用率: **89%** ⚠️
   - CPU 使用率: **67%** ✅

#### 解决方案

\`\`\`bash
# 1. 重启应用服务
sudo systemctl restart app-service

# 2. 清理缓存
redis-cli FLUSHALL

# 3. 检查数据库连接
mysql -u admin -p -e "SHOW PROCESSLIST;"
\`\`\`

#### 监控数据

| 时间段 | 错误率 | 响应时间 | 并发用户 |
|--------|--------|----------|----------|
| 09:00-10:00 | 2.3% | 1.2s | 1,234 |
| 10:00-11:00 | 15.7% | 3.8s | 2,456 |
| 11:00-12:00 | 8.9% | 2.1s | 1,890 |

#### 后续行动

- [ ] 优化数据库查询
- [ ] 增加服务器资源
- [ ] 完善监控告警
- [x] 更新故障处理文档

---

**状态**: 🟡 处理中
**预计解决时间**: 2小时内
**负责人**: @技术团队`;

    addBotMessage(mixedContent);
  }

  // 测试纯 Loading 状态（不自动移除）
  function testPureLoading() {
    if (chatRef.value && chatRef.value.messages) {
      // 添加用户消息
      chatRef.value.messages.push({
        type: 'user',
        content: '测试纯 Loading 状态',
        timestamp: Date.now(),
      });

      // 添加持续 loading 状态的机器人消息
      chatRef.value.messages.push({
        type: 'bot',
        content: '',
        timestamp: Date.now(),
        loading: true, // 保持 loading 状态
      });
    }
  }

  // 测试 Loading 状态
  function testLoading() {
    if (chatRef.value && chatRef.value.messages) {
      // 添加用户消息
      chatRef.value.messages.push({
        type: 'user',
        content: '请分析这个故障',
        timestamp: Date.now(),
      });

      // 添加 loading 状态的机器人消息
      const loadingMessage = {
        type: 'bot',
        content: '',
        timestamp: Date.now(),
        loading: true,
      };
      chatRef.value.messages.push(loadingMessage);

      // 模拟延迟后更新内容
      setTimeout(() => {
        const messageIndex = chatRef.value.messages.length - 1;
        if (messageIndex >= 0 && chatRef.value.messages[messageIndex].type === 'bot') {
          const updatedMessage = {
            ...chatRef.value.messages[messageIndex],
            content: `## 🔍 故障分析结果

经过分析，发现以下问题：

### 主要问题
- **数据库连接超时**: 连接池配置不当
- **内存使用率过高**: 存在内存泄漏

### 解决方案
\`\`\`javascript
// 优化连接池配置
const pool = new Pool({
  max: 20,
  min: 5,
  acquireTimeoutMillis: 30000
});
\`\`\`

分析完成时间: ${new Date().toLocaleString()}`,
            loading: false, // 移除 loading 状态
          };
          chatRef.value.messages.splice(messageIndex, 1, updatedMessage);
        }
      }, 3000); // 3秒后显示结果
    }
  }

  // 切换直接测试的 loading 状态
  function toggleDirectLoading() {
    const loadingItem = directTestItems.value.find((item) => item.key === 'loading');
    if (loadingItem) {
      loadingItem.loading = !loadingItem.loading;
    }
  }

  // 测试工作流事件处理
  function testWorkflowEvents() {
    if (chatRef.value && chatRef.value.messages) {
      // 添加用户消息
      chatRef.value.messages.push({
        type: 'user',
        content: '测试工作流事件处理',
        timestamp: Date.now(),
      });

      // 添加 loading 状态的机器人消息
      const loadingMessage = {
        type: 'bot',
        content: '',
        timestamp: Date.now(),
        loading: true,
      };
      chatRef.value.messages.push(loadingMessage);

      // 模拟工作流事件序列
      setTimeout(() => {
        console.log('模拟 workflow_started 事件');
        // workflow_started 事件不应该移除 loading 状态
      }, 1000);

      setTimeout(() => {
        console.log('模拟 node_started 事件');
        // node_started 事件不应该移除 loading 状态
      }, 2000);

      setTimeout(() => {
        console.log('模拟 node_finished 事件，包含实际内容');
        // 模拟接收到实际内容，这时才移除 loading 状态
        const messageIndex = chatRef.value.messages.length - 1;
        if (messageIndex >= 0 && chatRef.value.messages[messageIndex].type === 'bot') {
          const updatedMessage = {
            ...chatRef.value.messages[messageIndex],
            content: `## 🔍 工作流测试结果

### 事件处理验证
- ✅ workflow_started: 不显示内容，保持 loading
- ✅ node_started: 不显示内容，保持 loading
- ✅ node_finished: 显示实际内容，移除 loading

### 测试时间
${new Date().toLocaleString()}

这个测试验证了工作流事件的正确处理，确保只有在接收到实际内容时才移除 loading 状态。`,
            loading: false, // 移除 loading 状态
          };
          chatRef.value.messages.splice(messageIndex, 1, updatedMessage);
        }
      }, 4000);
    }
  }

  // 最简单的 Loading 测试
  function testSimpleLoading() {
    // 尝试不同的 loading 配置
    directTestItems.value = [
      {
        key: 'simple-loading',
        role: 'ai',
        loading: true,
        content: '', // 保留 content 字段以满足类型要求
      } as any,
    ];

    console.log('设置最简单的 loading 测试:', directTestItems.value);

    // 也尝试添加一个带 content 的 loading 项目
    setTimeout(() => {
      directTestItems.value.push({
        key: 'loading-with-content',
        role: 'ai',
        loading: true,
        content: 'Loading...',
      } as any);
      console.log('添加带内容的 loading 测试:', directTestItems.value);
    }, 2000);
  }

  // 测试文字提示功能
  function testThinkingText() {
    if (chatRef.value && chatRef.value.messages) {
      // 添加用户消息
      chatRef.value.messages.push({
        type: 'user',
        content: '测试文字提示功能',
        timestamp: Date.now(),
      });

      // 添加"正在思考"的机器人消息
      chatRef.value.messages.push({
        type: 'bot',
        content: '🤔 正在分析故障，请稍候...',
        timestamp: Date.now(),
        loading: false,
      });

      // 3秒后替换为实际内容
      setTimeout(() => {
        const messageIndex = chatRef.value.messages.length - 1;
        if (messageIndex >= 0 && chatRef.value.messages[messageIndex].type === 'bot') {
          const updatedMessage = {
            ...chatRef.value.messages[messageIndex],
            content: `## ✅ 文字提示测试成功！

刚才您看到的"🤔 正在分析故障，请稍候..."就是我们的文字提示功能。

### 优势
- ✅ 用户体验更直观
- ✅ 不依赖组件的 loading 状态
- ✅ 可以自定义提示文字和表情
- ✅ 兼容性更好

### 测试时间
${new Date().toLocaleString()}`,
            loading: false,
          };
          chatRef.value.messages.splice(messageIndex, 1, updatedMessage);
        }
      }, 3000);
    }
  }

  // 测试重复内容修复功能
  function testDuplicateContent() {
    if (chatRef.value && chatRef.value.messages) {
      // 添加用户消息
      chatRef.value.messages.push({
        type: 'user',
        content: '测试重复内容修复功能',
        timestamp: Date.now(),
      });

      // 添加"正在思考"的机器人消息
      const thinkingMessage = {
        type: 'bot',
        content: '🤔 正在分析故障，请稍候...',
        timestamp: Date.now(),
        loading: false,
        hasReceivedContent: false,
      };
      chatRef.value.messages.push(thinkingMessage);

      // 模拟第一次内容更新（应该替换思考文字）
      setTimeout(() => {
        console.log('第一次内容更新 - 应该替换思考文字');
        const messageIndex = chatRef.value.messages.length - 1;
        if (messageIndex >= 0 && chatRef.value.messages[messageIndex].type === 'bot') {
          const currentMessage = chatRef.value.messages[messageIndex];
          const updatedMessage = {
            ...currentMessage,
            content: '## 🔧 故障分析结果\n\n正在分析您的问题...',
            hasReceivedContent: true,
          };
          chatRef.value.messages.splice(messageIndex, 1, updatedMessage);
        }
      }, 1000);

      // 模拟第二次内容更新（应该被跳过，避免重复）
      setTimeout(() => {
        console.log('第二次内容更新 - 应该被跳过');
        const messageIndex = chatRef.value.messages.length - 1;
        if (messageIndex >= 0 && chatRef.value.messages[messageIndex].type === 'bot') {
          const currentMessage = chatRef.value.messages[messageIndex];

          // 模拟 setBotMessageContent 的调用
          if (currentMessage.hasReceivedContent) {
            console.log('✅ 检测到重复内容，已跳过:', '这是重复的内容');
          } else {
            const updatedMessage = {
              ...currentMessage,
              content: '这是重复的内容',
              hasReceivedContent: true,
            };
            chatRef.value.messages.splice(messageIndex, 1, updatedMessage);
          }
        }
      }, 2000);

      // 显示测试结果
      setTimeout(() => {
        const messageIndex = chatRef.value.messages.length - 1;
        if (messageIndex >= 0 && chatRef.value.messages[messageIndex].type === 'bot') {
          const currentMessage = chatRef.value.messages[messageIndex];
          const updatedMessage = {
            ...currentMessage,
            content: `## ✅ 重复内容修复测试完成！

### 测试结果
- ✅ 第一次内容更新：成功替换思考文字
- ✅ 第二次内容更新：成功跳过重复内容
- ✅ 防重复机制：正常工作

### 技术实现
- 使用 \`hasReceivedContent\` 标记跟踪消息状态
- \`updateBotMessageContent\` 处理首次内容更新
- \`setBotMessageContent\` 检查并跳过重复更新

### 测试时间
${new Date().toLocaleString()}`,
            hasReceivedContent: true,
          };
          chatRef.value.messages.splice(messageIndex, 1, updatedMessage);
        }
      }, 3500);
    }
  }

  // 添加机器人消息的辅助函数
  function addBotMessage(content: string) {
    if (chatRef.value && chatRef.value.messages) {
      chatRef.value.messages.push({
        type: 'bot',
        content: content,
        timestamp: Date.now(),
      });
    }
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 1rem;
  }

  .text-2xl {
    font-size: 1.5rem;
  }

  .font-bold {
    font-weight: 700;
  }

  .mb-4 {
    margin-bottom: 1rem;
  }

  .ml-2 {
    margin-left: 0.5rem;
  }

  .mt-2 {
    margin-top: 0.5rem;
  }

  .p-2 {
    padding: 0.5rem;
  }

  .bg-gray-100 {
    background-color: #f5f5f5;
  }

  .rounded {
    border-radius: 0.25rem;
  }

  .text-sm {
    font-size: 0.875rem;
  }

  .border {
    border: 1px solid #d9d9d9;
  }

  .text-lg {
    font-size: 1.125rem;
  }

  .font-semibold {
    font-weight: 600;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }
</style>
