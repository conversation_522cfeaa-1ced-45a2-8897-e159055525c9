<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">故障分析聊天组件测试</h1>
    <div class="bg-gray-100 p-4 rounded-lg mb-4">
      <p class="mb-2">这是一个测试页面，用于验证使用 antd-design-x-vue 重构的故障分析聊天组件。</p>
      <p class="mb-2">聊天气泡应该出现在页面右下角。</p>
      <p class="text-sm text-gray-600">测试故障ID: test-fault-123</p>
    </div>

    <!-- 测试按钮 -->
    <div class="mb-4">
      <a-button @click="openChat" type="primary">打开聊天窗口</a-button>
      <a-button @click="addTestMessage" class="ml-2">添加测试消息</a-button>
    </div>

    <!-- 故障分析聊天组件 -->
    <FaultAnalysisChat ref="chatRef" fault-id="test-fault-123" :fault-data="testFaultData" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import FaultAnalysisChat from '@/components/FaultAnalysisChat/index.vue';

  const chatRef = ref();

  // 测试数据
  const testFaultData = {
    faultDescribe: '测试故障：系统响应缓慢',
    stage: '已发布',
    startTime: '2024-01-01 10:00:00',
    recoverTime: '2024-01-01 12:00:00',
    problem: '数据库连接池配置不当导致系统响应缓慢',
  };

  // 测试函数
  function openChat() {
    console.log('尝试打开聊天窗口');
    if (chatRef.value) {
      chatRef.value.openChat();
    }
  }

  function addTestMessage() {
    console.log('添加测试消息');
    if (chatRef.value) {
      // 模拟添加一些测试消息
      chatRef.value.messages.push(
        {
          type: 'user',
          content: '请分析一下这个故障的根本原因',
          timestamp: Date.now(),
        },
        {
          type: 'bot',
          content: '',
          timestamp: Date.now(),
          loading: true,
        },
      );

      // 模拟 3 秒后加载完成
      setTimeout(() => {
        if (chatRef.value && chatRef.value.messages.length > 0) {
          const lastMessage = chatRef.value.messages[chatRef.value.messages.length - 1];
          if (lastMessage.type === 'bot' && lastMessage.loading) {
            lastMessage.loading = false;
            lastMessage.content =
              '根据您提供的故障信息，我分析如下：\n\n**故障原因分析：**\n1. 数据库连接池配置不当\n2. 系统负载过高\n3. 网络延迟问题\n\n**建议解决方案：**\n- 优化数据库连接池配置\n- 增加服务器资源\n- 检查网络连接';
          }
        }
      }, 3000);
    }
  }
</script>
