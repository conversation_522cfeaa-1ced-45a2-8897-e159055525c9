<template>
  <div class="fault-analysis-chat rounded-1">
    <!-- 气泡模式 -->
    <template v-if="mode === 'bubble'">
      <!-- 聊天气泡按钮 - 保留原来的样式 -->
      <div v-if="!isOpen" class="chat-bubble" @click="openChat" :style="bubbleStyle">
        <Icon icon="ant-design:message-outlined" size="24" color="white" />
      </div>

      <!-- 聊天窗口 - 保留原来的头部和圆角样式 -->
      <div v-if="isOpen" class="chat-window" :style="windowStyle">
        <!-- 聊天头部 - 保留原来的样式 -->
        <div class="chat-header">
          <span class="chat-title">故障分析助手</span>
          <div class="chat-actions">
            <Icon icon="ant-design:minus-outlined" @click="minimizeChat" class="action-icon" />
            <Icon icon="ant-design:close-outlined" @click="closeChat" class="action-icon" />
          </div>
        </div>

        <!-- 使用 antd-design-x-vue 的聊天组件 -->
        <div class="chat-container px-16px pb-16px pt-8px">
          <XProvider>
            <div style="display: flex; flex-direction: column; height: 100%; min-height: 0">
              <BubbleList
                :items="bubbleItems"
                :roles="bubbleRoles"
                :style="{ flex: 1, minHeight: 0 }"
                class="mb-8px pr-4px"
              />
              <Sender
                v-model:value="inputMessage"
                :loading="isLoading"
                placeholder="请输入您的问题..."
                @submit="sendMessage"
              >
                <template #actions="{ ori }">
                  <div
                    style="display: flex; align-items: center; justify-content: center; gap: 8px"
                  >
                    <a-button @click="resetSession" size="small" type="text"> 重置 </a-button>
                    <component :is="ori" />
                  </div>
                </template>
              </Sender>
            </div>
          </XProvider>
        </div>
      </div>
    </template>

    <!-- 内联模式 -->
    <template v-else>
      <div class="inline-chat-container">
        <XProvider>
          <div class="chat-content-wrapper">
            <div class="chat-messages-area">
              <BubbleList
                :items="bubbleItems"
                :roles="bubbleRoles"
                class="pr-4px h-full"
                :style="{ height: '100%', overflow: 'auto' }"
              />
            </div>
            <div class="chat-input-area">
              <Sender
                v-model:value="inputMessage"
                :loading="isLoading"
                placeholder="请输入您的问题..."
                @submit="sendMessage"
              >
                <template #actions="{ ori }">
                  <div
                    style="display: flex; align-items: center; justify-content: center; gap: 8px"
                  >
                    <a-button @click="resetSession" size="small" type="text"> 重置 </a-button>
                    <component :is="ori" />
                  </div>
                </template>
              </Sender>
            </div>
          </div>
        </XProvider>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, nextTick, h, watch } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { XProvider, BubbleList, Sender } from 'ant-design-x-vue';
  import type { BubbleProps } from 'ant-design-x-vue';
  import { Typography } from 'ant-design-vue';
  import { useUserStore } from '@/store/modules/user';
  import { useMessage } from '@/hooks/web/useMessage';
  import markdownit from 'markdown-it';
  import hljs from 'highlight.js/lib/core';
  // 导入常用语言
  import javascript from 'highlight.js/lib/languages/javascript';
  import typescript from 'highlight.js/lib/languages/typescript';
  import python from 'highlight.js/lib/languages/python';
  import java from 'highlight.js/lib/languages/java';
  import sql from 'highlight.js/lib/languages/sql';
  import bash from 'highlight.js/lib/languages/bash';
  import json from 'highlight.js/lib/languages/json';
  import xml from 'highlight.js/lib/languages/xml';
  import css from 'highlight.js/lib/languages/css';
  // 导入 highlight.js 样式
  import 'highlight.js/styles/github.css';

  interface Message {
    type: 'user' | 'bot';
    content: string;
    timestamp: number;
    loading?: boolean;
    hasReceivedContent?: boolean; // 标记是否已经接收到实际内容
  }

  interface Props {
    faultId: string;
    faultData?: any;
    position?: {
      bottom?: string;
      right?: string;
    };
    mode?: 'bubble' | 'inline'; // 新增模式控制
  }

  const props = withDefaults(defineProps<Props>(), {
    position: () => ({ bottom: '2rem', right: '2rem' }),
    mode: 'bubble',
  });

  const { createMessage } = useMessage();
  const { error } = createMessage;

  // 注册 highlight.js 语言
  hljs.registerLanguage('javascript', javascript);
  hljs.registerLanguage('typescript', typescript);
  hljs.registerLanguage('python', python);
  hljs.registerLanguage('java', java);
  hljs.registerLanguage('sql', sql);
  hljs.registerLanguage('bash', bash);
  hljs.registerLanguage('json', json);
  hljs.registerLanguage('xml', xml);
  hljs.registerLanguage('css', css);

  // 初始化 markdown-it 实例，配置代码高亮（表格默认支持）
  const md = markdownit({
    html: true,
    breaks: true,
    linkify: true,
    typographer: true,
    highlight: function (str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;
        } catch (__) {
          // 忽略高亮错误，返回原始内容
        }
      }
      return ''; // 使用外部默认转义
    },
  });

  // 自定义 markdown 渲染函数
  const renderMarkdown: BubbleProps['messageRender'] = (content) => {
    return h(
      Typography,
      {},
      {
        default: () =>
          h('div', {
            innerHTML: md.render(String(content || '')),
            class: 'markdown-content',
          }),
      },
    );
  };

  const isOpen = ref(false);
  const inputMessage = ref('');
  const messages = ref<Message[]>([]);
  const isLoading = ref(false);

  // 转换消息格式为 BubbleList 的格式
  const bubbleItems = computed(() => {
    const items = messages.value.map((message, index) => {
      const role = message.type === 'user' ? 'user' : 'ai';

      const item: any = {
        key: `message-${index}`,
        role: role,
        content: message.content,
      };

      return item;
    });

    return items;
  });

  // BubbleList 的角色配置 - 完全使用 antd-design-x-vue 的样式系统
  const bubbleRoles = computed(() => {
    const roles = {
      ai: {
        placement: 'start' as const,
        avatar: {
          icon: h(Icon, { icon: 'ant-design:robot-outlined' }),
          style: { background: '#1677ff' },
        },
        typing: { step: 5, interval: 20 },
        variant: 'filled' as const,
        messageRender: renderMarkdown, // 使用 markdown 渲染
        styles: {
          content: {
            fontSize: '14px',
            lineHeight: '1.5',
            // 确保表格等内容有足够空间
            maxWidth: 'none',
            overflow: 'visible',
            wordBreak: 'break-word',
          },
        },
      },

      user: {
        placement: 'end' as const,
        avatar: {
          icon: h(Icon, { icon: 'ant-design:user-outlined' }),
          style: { background: '#52c41a' },
        },
        variant: 'filled' as const,
        messageRender: (content: any) => h('div', {}, String(content || '')), // 简单文本渲染
        styles: {
          content: {
            fontSize: '14px',
            lineHeight: '1.5',
          },
        },
      },
    };

    return roles;
  });

  // 气泡样式 - 保留原来的样式
  const bubbleStyle = computed(() => ({
    position: 'fixed' as const,
    bottom: props.position.bottom,
    right: props.position.right,
    backgroundColor: '#155EEF',
    width: '60px',
    height: '60px',
    borderRadius: '30px',
    boxShadow: 'rgba(0, 0, 0, 0.2) 0px 4px 8px 0px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  }));

  // 窗口样式 - 保留原来的样式
  const windowStyle = computed(() => ({
    position: 'fixed' as const,
    bottom: props.position.bottom,
    right: props.position.right,
    width: '600px',
    height: '80%',
    backgroundColor: 'white',
    borderRadius: '12px',
    boxShadow: 'rgba(0, 0, 0, 0.15) 0px 8px 24px 0px',
    zIndex: 1000,
    display: 'flex',
    flexDirection: 'column' as const,
  }));

  // 生成故障链接的Markdown格式
  function generateFaultLinks() {
    const faultIds = props.faultId.includes(',') ? props.faultId.split(',') : [props.faultId];
    const faultDataArray = Array.isArray(props.faultData) ? props.faultData : [props.faultData];

    return faultIds.map((faultId) => {
      // 查找对应的故障数据
      const faultInfo = faultDataArray.find((item) => item?.fid === faultId);
      const faultTitle = faultInfo?.faultDescribe || faultId;
      const faultLink = `/fault/detail/${faultId}`;

      // 返回Markdown格式的链接
      return `[${faultTitle}](${faultLink})`;
    });
  }

  // 生成欢迎消息内容
  function generateWelcomeMessage() {
    const allFaultLinks = generateFaultLinks();
    const maxAnalysisCount = 10; // 最多分析10个故障
    const maxDisplayCount = 10; // 最多展示10个故障
    const displayFaultLinks = allFaultLinks.slice(0, maxDisplayCount);
    const totalCount = allFaultLinks.length;

    let welcomeText = '';

    // 检查是否超过分析限制
    if (totalCount > maxAnalysisCount) {
      welcomeText = `您好！我是故障分析助手。\n\n⚠️ **重要提示：您当前选择了 ${totalCount} 个故障，但我暂时只能分析最多 ${maxAnalysisCount} 个故障。**\n\n为了获得更准确的分析结果，建议您：\n1. 减少选择的故障数量至 ${maxAnalysisCount} 个以内\n2. 或者分批次进行分析\n\n如果您继续当前操作，我将只分析前 ${maxAnalysisCount} 个故障：\n\n${displayFaultLinks.join('\n\n')}\n\n请告诉我您想了解什么？`;
    } else if (totalCount === 1) {
      welcomeText = `您好！我是故障分析助手，正在为您分析故障：\n\n${displayFaultLinks[0]}\n\n请告诉我您想了解什么？`;
    } else {
      const faultList = displayFaultLinks.join('\n\n');
      if (totalCount > maxDisplayCount) {
        welcomeText = `您好！我是故障分析助手，正在为您分析以下 ${totalCount} 个故障（显示前${maxDisplayCount}个）：\n\n${faultList}\n\n... 还有 ${totalCount - maxDisplayCount} 个故障\n\n请告诉我您想了解什么？我可以帮您进行综合分析。`;
      } else {
        welcomeText = `您好！我是故障分析助手，正在为您分析以下 ${totalCount} 个故障：\n\n${faultList}\n\n请告诉我您想了解什么？我可以帮您进行综合分析。`;
      }
    }

    return welcomeText;
  }

  // 更新欢迎消息（仅更新第一条机器人消息）
  function updateWelcomeMessage() {
    if (messages.value.length > 0 && messages.value[0].type === 'bot') {
      const newWelcomeText = generateWelcomeMessage();

      // 创建新的消息对象以确保响应式更新
      const updatedMessage = {
        ...messages.value[0],
        content: newWelcomeText,
        timestamp: Date.now(),
      };

      // 使用splice替换第一条消息以确保响应式更新
      messages.value.splice(0, 1, updatedMessage);

      // 强制触发响应式更新
      nextTick();
    }
  }

  // 打开聊天窗口
  function openChat() {
    isOpen.value = true;
    if (messages.value.length === 0) {
      // 添加欢迎消息
      const welcomeText = generateWelcomeMessage();

      messages.value.push({
        type: 'bot',
        content: welcomeText,
        timestamp: Date.now(),
      });
    }
  }

  // 最小化聊天窗口
  function minimizeChat() {
    isOpen.value = false;
  }

  // 关闭聊天窗口
  function closeChat() {
    isOpen.value = false;
    messages.value = [];
  }

  // 重置会话
  function resetSession() {
    messages.value = [];
    inputMessage.value = '';
    // 添加欢迎消息
    const welcomeText = generateWelcomeMessage();

    messages.value.push({
      type: 'bot',
      content: welcomeText,
      timestamp: Date.now(),
    });
  }

  // 发送消息 - 适配 antd-design-x-vue 的 Sender 组件
  async function sendMessage(content: string) {
    if (!content.trim() || isLoading.value) return;

    const userMessage = content.trim();

    // 检查故障数量限制
    const faultIdsArray = props.faultId.includes(',') ? props.faultId.split(',') : [props.faultId];
    const maxAnalysisCount = 10;

    // 添加用户消息
    messages.value.push({
      type: 'user',
      content: userMessage,
      timestamp: Date.now(),
    });

    // 如果超过限制，先添加提醒消息
    if (faultIdsArray.length > maxAnalysisCount) {
      const reminderMessage: Message = {
        type: 'bot',
        content: `⚠️ 提醒：您选择了 ${faultIdsArray.length} 个故障，我将只分析前 ${maxAnalysisCount} 个故障。\n\n🤔 正在分析故障，请稍候...`,
        timestamp: Date.now(),
        loading: false,
      };
      messages.value.push(reminderMessage);
    } else {
      // 添加思考中的机器人消息，使用文字提示
      const botMessage: Message = {
        type: 'bot',
        content: '🤔 正在分析故障，请稍候...',
        timestamp: Date.now(),
        loading: false, // 不使用 loading 状态，改用文字提示
      };
      messages.value.push(botMessage);
    }

    // 获取最后添加的机器人消息作为要更新的消息
    const botMessage = messages.value[messages.value.length - 1] as Message;

    // 调用SSE流式API
    try {
      isLoading.value = true;

      await callStreamingAPI(userMessage, botMessage);
    } catch (err) {
      console.error('故障分析API调用失败:', err);
      // 更新最后一条消息为错误消息，移除 loading 状态
      const messageIndex = messages.value.length - 1;
      if (messageIndex >= 0 && messages.value[messageIndex].type === 'bot') {
        const updatedMessage = {
          ...messages.value[messageIndex],
          content: '抱歉，服务暂时不可用，请稍后再试。',
          loading: false, // 移除 loading 状态
        };
        messages.value.splice(messageIndex, 1, updatedMessage);
      } else {
        // 如果没有找到对应的消息，则添加新的错误消息
        messages.value.push({
          type: 'bot',
          content: '抱歉，服务暂时不可用，请稍后再试。',
          timestamp: Date.now(),
          loading: false,
        });
      }
      error('故障分析服务调用失败');
    } finally {
      isLoading.value = false;
      // 清空输入框
      inputMessage.value = '';
    }
  }

  // SSE流式API调用
  async function callStreamingAPI(userMessage: string, botMessage: Message) {
    const userStore = useUserStore();
    const userInfo = userStore.getUserInfo;

    // 处理故障ID - 如果有多个ID，用逗号分隔的字符串形式传递
    const faultIdsArray = props.faultId.includes(',') ? props.faultId.split(',') : [props.faultId];

    // 限制最多分析10个故障
    const maxAnalysisCount = 10;
    const limitedFaultIdsArray = faultIdsArray.slice(0, maxAnalysisCount);
    const faultIdsString = limitedFaultIdsArray.join(','); // 转换为逗号分隔的字符串

    const requestBody = {
      response_mode: 'streaming',
      user: userInfo.email,
      inputs: {
        faultIds: faultIdsString, // 传递字符串格式的故障ID列表（最多10个）
        faultCount: limitedFaultIdsArray.length,
        originalFaultCount: faultIdsArray.length, // 原始故障数量，用于后端了解实际选择情况
        env: process.env.NODE_ENV,
      },
      query: userMessage,
    };

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时

    try {
      const response = await fetch('/api/dify/workflows/run/fault_report_analysis/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'text/event-stream',
          token: 'LLM',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API错误响应:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      // 检查Content-Type是否为SSE格式
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('text/event-stream')) {
        // 先读取为文本，然后尝试解析为JSON
        const responseText = await response.text();

        // 尝试解析为JSON
        try {
          const jsonResponse = JSON.parse(responseText);

          // 处理JSON格式的响应
          let content = '';
          if (jsonResponse.code !== undefined) {
            if (
              jsonResponse.code === 200 ||
              jsonResponse.code === 0 ||
              jsonResponse.code === '200' ||
              jsonResponse.code === '0'
            ) {
              if (jsonResponse.data) {
                if (typeof jsonResponse.data === 'string') {
                  content = jsonResponse.data;
                } else if (jsonResponse.data.answer) {
                  content = jsonResponse.data.answer;
                } else if (jsonResponse.data.outputs?.answer) {
                  content = jsonResponse.data.outputs.answer;
                }
              }
            } else {
              const errorMsg = jsonResponse.msg || jsonResponse.message || '未知错误';
              content = `错误 (${jsonResponse.code}): ${errorMsg}`;
            }
          } else if (jsonResponse.answer) {
            content = jsonResponse.answer;
          } else if (jsonResponse.data?.answer) {
            content = jsonResponse.data.answer;
          }

          if (content) {
            botMessage.content = content;
          } else {
            botMessage.content = '抱歉，无法解析服务器响应。';
          }
          return;
        } catch (jsonError) {
          // 如果JSON解析失败，直接使用文本内容
          botMessage.content = responseText || '服务器返回了无效的响应格式。';
          return;
        }
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      await processSSEStream(reader, botMessage);
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  }

  // 处理SSE流数据
  async function processSSEStream(
    reader: ReadableStreamDefaultReader<Uint8Array>,
    botMessage: Message,
  ) {
    const decoder = new TextDecoder();
    let buffer = '';
    let hasReceivedContent = false;

    try {
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          break;
        }

        // 解码数据块
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 处理完整的SSE事件
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留不完整的行

        for (const line of lines) {
          if (line.trim() === '') continue; // 跳过空行

          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim(); // 移除 'data: ' 前缀并去除空格

            if (data === '[DONE]' || data === 'data: [DONE]') {
              // 流结束
              return;
            }

            if (data === '') continue; // 跳过空数据

            try {
              const parsed = JSON.parse(data);

              // 处理Dify工作流的不同事件类型，只有在真正添加内容时才标记为已接收
              const actuallyAddedContent = await handleDifyWorkflowEvent(parsed, botMessage);
              if (actuallyAddedContent) {
                hasReceivedContent = true;
              }
            } catch (parseError) {
              // 如果解析失败，记录错误但不显示原始数据
              // 这样可以避免显示不必要的事件数据流信息
              console.log('SSE数据解析失败:', data, parseError);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // 检查最后一条机器人消息的内容
    const lastBotMessageIndex = messages.value.length - 1;
    const lastBotMessage = messages.value[lastBotMessageIndex];

    // 如果没有收到任何内容，或者最后的机器人消息内容为空
    if (!hasReceivedContent || !lastBotMessage || !lastBotMessage.content.trim()) {
      setBotMessageContent(botMessage, '抱歉，我暂时无法回答您的问题，请稍后再试。');
    }
  }

  // 处理Dify工作流事件
  async function handleDifyWorkflowEvent(event: any, botMessage: Message): Promise<boolean> {
    let contentAdded = false;

    switch (event.event) {
      case 'workflow_started':
        // 工作流开始，不显示任何内容，保持 loading 状态
        // 不设置 contentAdded = true，保持 loading 状态
        break;

      case 'node_started':
        // 节点开始，不显示执行进度和事件时间，保持 loading 状态
        // 不设置 contentAdded = true，保持 loading 状态
        break;

      case 'node_finished':
        // 处理节点完成后的输出
        if (event.data?.outputs) {
          const outputs = event.data.outputs;

          // 查找可能包含回答内容的字段
          let content = '';
          if (outputs.answer) {
            content = outputs.answer;
          } else if (outputs.text) {
            content = outputs.text;
          } else if (outputs.content) {
            content = outputs.content;
          } else if (outputs.result) {
            content = outputs.result;
          } else if (typeof outputs === 'string') {
            content = outputs;
          }

          // 确保 content 是字符串类型，并且有实际内容
          if (content && typeof content === 'string' && content.trim()) {
            // 如果内容不是简单的输入回显，则添加到消息中
            if (!isInputEcho(content, event.data?.inputs)) {
              // node_finished 事件通常包含完整的节点输出，使用替换模式
              setBotMessageContent(botMessage, content);
              contentAdded = true;
            }
          } else if (content && typeof content !== 'string') {
            // 如果 content 不是字符串（如布尔值、数字等），转换为字符串但不处理
            console.log('跳过非字符串内容:', typeof content, content);
          }
        }
        break;

      case 'workflow_finished':
        // 处理工作流完成事件
        if (event.data?.outputs) {
          const outputs = event.data.outputs;
          let finalContent = '';

          // 尝试多种可能的字段名
          if (outputs.answer) {
            finalContent = outputs.answer;
          } else if (outputs.text) {
            finalContent = outputs.text;
          } else if (outputs.content) {
            finalContent = outputs.content;
          } else if (outputs.result) {
            finalContent = outputs.result;
          } else if (outputs.response) {
            finalContent = outputs.response;
          } else if (outputs.output) {
            finalContent = outputs.output;
          } else {
            // 如果没有找到标准字段，尝试遍历所有字段
            for (const [, value] of Object.entries(outputs)) {
              if (typeof value === 'string' && value.trim().length > 10) {
                finalContent = value;
                break;
              }
            }
          }

          // 确保 finalContent 是字符串类型，并且有实际内容
          if (finalContent && typeof finalContent === 'string' && finalContent.trim()) {
            // 设置最终内容
            setBotMessageContent(botMessage, finalContent);
            contentAdded = true;
          } else if (finalContent && typeof finalContent !== 'string') {
            // 如果 finalContent 不是字符串，记录但不处理
            console.log('跳过非字符串的最终内容:', typeof finalContent, finalContent);
          }
        }
        break;

      case 'text_chunk':
      case 'agent_message':
      case 'message':
        // 处理流式文本块
        let streamContent = '';
        if (event.answer) {
          streamContent = event.answer;
        } else if (event.data?.answer) {
          streamContent = event.data.answer;
        } else if (event.content) {
          streamContent = event.content;
        } else if (event.data?.content) {
          streamContent = event.data.content;
        } else if (event.text) {
          streamContent = event.text;
        } else if (event.data?.text) {
          streamContent = event.data.text;
        }

        // 确保 streamContent 是字符串类型
        if (streamContent && typeof streamContent === 'string') {
          updateBotMessageContent(botMessage, streamContent);
          contentAdded = true;
        } else if (streamContent && typeof streamContent !== 'string') {
          // 如果 streamContent 不是字符串，记录但不处理
          console.log('跳过非字符串的流式内容:', typeof streamContent, streamContent);
        }
        break;

      case 'message_end':
        // message_end 事件标志着整个对话的结束，但不包含内容
        // 如果到此时还没有内容，保持 loading 状态直到超时处理
        break;

      case 'error':
        const errorMsg = event.data?.message || event.data?.error || '工作流执行出错';
        updateBotMessageContent(botMessage, `\n错误: ${errorMsg}\n`);
        contentAdded = true;
        break;

      default:
        // 对于未知事件类型，不处理任何内容，保持 loading 状态
        // 这样可以避免显示不必要的事件数据流信息
        console.log('未处理的事件类型:', event.event, event);
        break;
    }

    return contentAdded;
  }

  // 更新机器人消息内容（智能模式：首次替换，后续追加）
  function updateBotMessageContent(_botMessage: Message, newContent: string) {
    // 找到最后一条机器人消息（应该就是当前正在更新的消息）
    const messageIndex = messages.value.length - 1;

    if (messageIndex >= 0 && messages.value[messageIndex].type === 'bot') {
      const currentMessage = messages.value[messageIndex];

      // 如果当前消息是"正在思考"状态，则替换为新内容
      const isThinking =
        currentMessage.content.includes('正在分析故障') ||
        currentMessage.content.includes('正在思考') ||
        currentMessage.content.includes('请稍候');

      let finalContent: string;
      if (isThinking) {
        // 首次接收内容，替换思考文字
        finalContent = newContent;
      } else if (currentMessage.hasReceivedContent) {
        // 已经有内容，追加新内容（用于流式更新）
        finalContent = currentMessage.content + newContent;
      } else {
        // 其他情况，直接设置内容
        finalContent = newContent;
      }

      const updatedMessage = {
        ...currentMessage,
        content: finalContent,
        loading: false, // 移除 loading 状态
        hasReceivedContent: true, // 标记已经接收到实际内容
      };

      // 使用splice替换消息以确保响应式更新
      messages.value.splice(messageIndex, 1, updatedMessage);

      // 强制触发响应式更新
      nextTick();
    }
  }

  // 设置机器人消息内容（替换模式）
  function setBotMessageContent(_botMessage: Message, content: string) {
    // 找到最后一条机器人消息（应该就是当前正在更新的消息）
    const messageIndex = messages.value.length - 1;

    if (messageIndex >= 0 && messages.value[messageIndex].type === 'bot') {
      const currentMessage = messages.value[messageIndex];

      // 如果已经接收到内容，则不再替换，避免重复
      if (currentMessage.hasReceivedContent) {
        return;
      }

      // 创建新的消息对象以确保响应式更新
      const updatedMessage = {
        ...currentMessage,
        content: content,
        loading: false, // 移除 loading 状态
        hasReceivedContent: true, // 标记已经接收到实际内容
      };

      // 使用splice替换消息以确保响应式更新
      messages.value.splice(messageIndex, 1, updatedMessage);

      // 强制触发响应式更新
      nextTick();
    }
  }

  // 检查内容是否只是输入的回显
  function isInputEcho(content: string, inputs: any): boolean {
    if (!inputs || !content) return false;

    // 检查内容是否与输入字段匹配
    const inputValues = Object.values(inputs).filter((v) => typeof v === 'string');
    return inputValues.some((input) => content.includes(input as string));
  }

  // 初始化：如果是内联模式，自动添加欢迎消息
  if (props.mode === 'inline') {
    const welcomeText = generateWelcomeMessage();

    messages.value.push({
      type: 'bot',
      content: welcomeText,
      timestamp: Date.now(),
    });
  }

  // 监听故障ID和故障数据的变化，自动更新欢迎消息
  watch(
    [() => props.faultId, () => props.faultData],
    () => {
      // 只有在内联模式下才自动更新欢迎消息
      if (props.mode === 'inline') {
        updateWelcomeMessage();
      }
    },
    { deep: true },
  );

  // 暴露方法和数据给父组件
  defineExpose({
    openChat,
    closeChat,
    minimizeChat,
    resetSession,
    messages,
    isOpen,
  });
</script>

<style lang="less" scoped>
  .fault-analysis-chat {
    .chat-bubble {
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }

    .chat-window {
      .chat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-radius: 12px 12px 0 0;
        background-color: #155eef;
        color: white;

        .chat-title {
          font-weight: 500;
        }

        .chat-actions {
          display: flex;
          gap: 8px;

          .action-icon {
            padding: 4px;
            transition: background-color 0.2s;
            border-radius: 4px;
            cursor: pointer;

            &:hover {
              background-color: rgb(255 255 255 / 10%);
            }
          }
        }
      }

      .chat-container {
        display: flex;
        flex: 1;
        flex-direction: column;
        height: calc(100% - 60px); // 减去头部高度
        overflow: hidden;
      }
    }

    .inline-chat-container {
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 100%;
      padding: 16px;
      overflow: hidden;
      background-color: white;
    }

    .chat-content-wrapper {
      display: flex;
      flex-direction: column;
      height: 100%;
      min-height: 0;
    }

    .chat-messages-area {
      flex: 1;
      min-height: 0;
      margin-bottom: 8px;
      overflow: hidden;
    }

    .chat-input-area {
      flex-shrink: 0;
    }
  }

  // Markdown 内容样式
  :deep(.markdown-content) {
    // 标题样式
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin: 0.5em 0;
      font-weight: 600;
      line-height: 1.25;
    }

    h1 {
      font-size: 1.5em;
    }

    h2 {
      font-size: 1.3em;
    }

    h3 {
      font-size: 1.1em;
    }

    h4,
    h5,
    h6 {
      font-size: 1em;
    }

    // 段落样式
    p {
      margin: 0.5em 0;
      line-height: 1.6;
    }

    // 列表样式
    ul,
    ol {
      margin: 0.5em 0;
      padding-left: 1.5em;
    }

    li {
      margin: 0.25em 0;
    }

    // 代码样式
    code {
      padding: 0.2em 0.4em;
      border-radius: 3px;
      background-color: #f6f8fa;
      color: #24292f;
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 0.85em;
    }

    pre {
      margin: 0.5em 0;
      padding: 1em;
      overflow-x: auto;
      border: 1px solid #d0d7de;
      border-radius: 6px;
      background-color: #f6f8fa;

      code {
        padding: 0;
        background: none;
        color: inherit;
        font-size: 0.875em;
        line-height: 1.45;
      }
    }

    // highlight.js 代码高亮样式增强
    .hljs {
      padding: 1em;
      overflow-x: auto;
      border-radius: 6px;
      background: #f6f8fa !important;
      color: #24292f !important;
    }

    // 引用样式
    blockquote {
      margin: 0.5em 0;
      padding-left: 1em;
      border-left: 4px solid #ddd;
      color: #666;
      font-style: italic;
    }

    // 链接样式
    a {
      color: #1677ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    // 表格样式 - 增强兼容性和显示效果
    table {
      // 防止被其他样式覆盖
      display: table !important;
      width: 100%;
      margin: 0.5em 0;
      // 确保表格不被容器截断
      table-layout: auto;
      border-spacing: 0;
      border-collapse: collapse;
      font-size: inherit;
      line-height: inherit;
      word-break: break-word;
    }

    th,
    td {
      // 确保单元格正确显示
      display: table-cell !important;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      text-align: left;
      // 处理长文本
      word-wrap: break-word;
      word-break: break-word;
      vertical-align: top;
    }

    th {
      // 确保表头样式不被覆盖
      border-bottom: 2px solid #d9d9d9;
      background-color: #fafafa;
      color: rgb(0 0 0 / 85%);
      font-weight: 600;
    }

    // 表格容器样式
    .table-container {
      margin: 0.5em 0;
      overflow-x: auto;
    }

    // 分隔线样式
    hr {
      margin: 1em 0;
      border: none;
      border-top: 1px solid #ddd;
    }

    // 强调样式
    strong,
    b {
      font-weight: 600;
    }

    em,
    i {
      font-style: italic;
    }
  }
</style>
