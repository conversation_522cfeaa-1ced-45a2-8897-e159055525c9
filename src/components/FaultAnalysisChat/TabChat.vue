<template>
  <div class="tab-chat-container h-full flex flex-col">
    <!-- 聊天头部 -->
    <div class="chat-header flex items-center justify-between p-3 bg-blue-50">
      <span class="font-medium text-gray-700">故障分析助手</span>
      <div class="flex items-center gap-2">
        <span v-if="currentFaultIds.length > 0" class="text-sm text-gray-500">
          当前故障:
          {{
            currentFaultIds.length === 1 ? currentFaultIds[0] : `${currentFaultIds.length}个故障`
          }}
        </span>
        <a-button size="small" type="text" @click="resetSession">
          <template #icon>
            <Icon icon="ant-design:reload-outlined" />
          </template>
          重置
        </a-button>
      </div>
    </div>

    <!-- 聊天内容区域 -->
    <div class="chat-content flex-1 min-h-0 p-2">
      <XProvider>
        <div class="h-full flex flex-col">
          <BubbleList
            :items="bubbleItems"
            :roles="bubbleRoles"
            :style="{ flex: 1, minHeight: 0 }"
            class="mb-2"
          />
          <Sender
            v-model:value="inputMessage"
            :loading="isLoading"
            placeholder="请输入您的问题..."
            @submit="sendMessage"
          >
            <template #actions="{ ori }">
              <div class="flex items-center justify-center gap-2">
                <a-button @click="resetSession" size="small" type="text">重置</a-button>
                <component :is="ori" />
              </div>
            </template>
          </Sender>
        </div>
      </XProvider>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, h, nextTick } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { XProvider, BubbleList, Sender } from 'ant-design-x-vue';
  import type { BubbleProps } from 'ant-design-x-vue';
  import { Typography, Button as AButton } from 'ant-design-vue';
  import { useUserStore } from '@/store/modules/user';
  import { useMessage } from '@/hooks/web/useMessage';
  import markdownit from 'markdown-it';
  import hljs from 'highlight.js/lib/core';
  // 导入常用语言
  import javascript from 'highlight.js/lib/languages/javascript';
  import typescript from 'highlight.js/lib/languages/typescript';
  import python from 'highlight.js/lib/languages/python';
  import java from 'highlight.js/lib/languages/java';
  import sql from 'highlight.js/lib/languages/sql';
  import bash from 'highlight.js/lib/languages/bash';
  import json from 'highlight.js/lib/languages/json';
  import xml from 'highlight.js/lib/languages/xml';
  import css from 'highlight.js/lib/languages/css';
  // 导入 highlight.js 样式
  import 'highlight.js/styles/github.css';

  interface Message {
    type: 'user' | 'bot';
    content: string;
    timestamp: number;
    loading?: boolean;
    hasReceivedContent?: boolean;
  }

  interface Props {
    faultIds?: string[];
    faultDataList?: any[];
  }

  const props = withDefaults(defineProps<Props>(), {
    faultIds: () => [],
    faultDataList: () => [],
  });

  const { createMessage } = useMessage();
  const { error } = createMessage;

  // 注册 highlight.js 语言
  hljs.registerLanguage('javascript', javascript);
  hljs.registerLanguage('typescript', typescript);
  hljs.registerLanguage('python', python);
  hljs.registerLanguage('java', java);
  hljs.registerLanguage('sql', sql);
  hljs.registerLanguage('bash', bash);
  hljs.registerLanguage('json', json);
  hljs.registerLanguage('xml', xml);
  hljs.registerLanguage('css', css);

  // 初始化 markdown-it 实例（表格默认支持）
  const md = markdownit({
    html: true,
    breaks: true,
    linkify: true,
    typographer: true,
    highlight: function (str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;
        } catch (__) {
          // 忽略高亮错误，返回原始内容
        }
      }
      return '';
    },
  });

  // 自定义 markdown 渲染函数
  const renderMarkdown: BubbleProps['messageRender'] = (content) => {
    try {
      return h(
        Typography,
        {},
        {
          default: () =>
            h('div', {
              innerHTML: md.render(String(content || '')),
              class: 'markdown-content',
            }),
        },
      );
    } catch (error) {
      console.error('Markdown渲染错误:', error);
      // 降级到简单文本渲染
      return h('div', {}, String(content || ''));
    }
  };

  const inputMessage = ref('');
  const messages = ref<Message[]>([]);
  const isLoading = ref(false);
  const currentFaultIds = ref<string[]>(props.faultIds);

  // 监听faultIds变化
  watch(
    () => props.faultIds,
    (newFaultIds) => {
      if (newFaultIds && JSON.stringify(newFaultIds) !== JSON.stringify(currentFaultIds.value)) {
        currentFaultIds.value = [...newFaultIds];
        // 当故障ID列表变化时，重置会话并添加新的欢迎消息
        resetSessionForNewFault();
      }
    },
    { immediate: true, deep: true },
  );

  // 转换消息格式为 BubbleList 的格式
  const bubbleItems = computed(() => {
    return messages.value.map((message, index) => ({
      key: `message-${index}`,
      role: message.type === 'user' ? 'user' : 'ai',
      content: message.content,
    }));
  });

  // BubbleList 的角色配置
  const bubbleRoles = computed(() => {
    try {
      return {
        ai: {
          placement: 'start' as const,
          avatar: {
            icon: h(Icon, { icon: 'ant-design:robot-outlined' }),
            style: { background: '#1677ff' },
          },
          typing: { step: 5, interval: 20 },
          variant: 'filled' as const,
          messageRender: renderMarkdown,
          styles: {
            content: {
              fontSize: '14px',
              lineHeight: '1.5',
            },
          },
        },
        user: {
          placement: 'end' as const,
          avatar: {
            icon: h(Icon, { icon: 'ant-design:user-outlined' }),
            style: { background: '#52c41a' },
          },
          variant: 'filled' as const,
          messageRender: (content: any) => h('div', {}, String(content || '')),
          styles: {
            content: {
              fontSize: '14px',
              lineHeight: '1.5',
            },
          },
        },
      };
    } catch (error) {
      console.error('BubbleRoles配置错误:', error);
      // 返回简化的配置
      return {
        ai: {
          placement: 'start' as const,
          variant: 'filled' as const,
        },
        user: {
          placement: 'end' as const,
          variant: 'filled' as const,
        },
      };
    }
  });

  // 初始化欢迎消息
  function initWelcomeMessage() {
    if (messages.value.length === 0) {
      let welcomeText = '';
      const maxDisplayCount = 10; // 最多展示10个故障
      const totalCount = currentFaultIds.value.length;

      if (totalCount === 0) {
        welcomeText =
          '您好！我是故障分析助手，可以帮助您分析故障报告。请先选择一个或多个故障，然后告诉我您想了解什么？';
      } else if (totalCount === 1) {
        welcomeText = `您好！我是故障分析助手，正在为您分析故障 ${currentFaultIds.value[0]}。请告诉我您想了解什么？`;
      } else {
        const displayFaultIds = currentFaultIds.value.slice(0, maxDisplayCount);
        const faultList = displayFaultIds.join('、');

        if (totalCount > maxDisplayCount) {
          welcomeText = `您好！我是故障分析助手，正在为您分析以下 ${totalCount} 个故障（显示前${maxDisplayCount}个）：\n\n${faultList}\n\n... 还有 ${totalCount - maxDisplayCount} 个故障\n\n请告诉我您想了解什么？我可以帮您进行综合分析。`;
        } else {
          welcomeText = `您好！我是故障分析助手，正在为您分析以下 ${totalCount} 个故障：\n\n${faultList}\n\n请告诉我您想了解什么？我可以帮您进行综合分析。`;
        }
      }

      messages.value.push({
        type: 'bot',
        content: welcomeText,
        timestamp: Date.now(),
      });
    }
  }

  // 重置会话
  function resetSession() {
    messages.value = [];
    inputMessage.value = '';
    initWelcomeMessage();
  }

  // 为新故障重置会话
  function resetSessionForNewFault() {
    messages.value = [];
    inputMessage.value = '';
    initWelcomeMessage();
  }

  // 发送消息
  async function sendMessage(content: string) {
    if (!content.trim() || isLoading.value) return;

    if (currentFaultIds.value.length === 0) {
      error('请先选择一个或多个故障');
      return;
    }

    const userMessage = content.trim();

    // 添加用户消息
    messages.value.push({
      type: 'user',
      content: userMessage,
      timestamp: Date.now(),
    });

    // 添加思考中的机器人消息
    const botMessage: Message = {
      type: 'bot',
      content: '🤔 正在分析故障，请稍候...',
      timestamp: Date.now(),
      loading: false,
    };
    messages.value.push(botMessage);

    // 调用API
    try {
      isLoading.value = true;
      await callStreamingAPI(userMessage, botMessage);
    } catch (err) {
      console.error('故障分析API调用失败:', err);
      const messageIndex = messages.value.length - 1;
      if (messageIndex >= 0 && messages.value[messageIndex].type === 'bot') {
        const updatedMessage = {
          ...messages.value[messageIndex],
          content: '抱歉，服务暂时不可用，请稍后再试。',
          loading: false,
        };
        messages.value.splice(messageIndex, 1, updatedMessage);
      }
      error('故障分析服务调用失败');
    } finally {
      isLoading.value = false;
      inputMessage.value = '';
    }
  }

  // SSE流式API调用
  async function callStreamingAPI(userMessage: string, botMessage: Message) {
    const userStore = useUserStore();
    const userInfo = userStore.getUserInfo;

    const requestBody = {
      response_mode: 'streaming',
      user: userInfo.email,
      inputs: {
        faultIds: currentFaultIds.value, // 传递故障ID数组
        faultCount: currentFaultIds.value.length, // 故障数量
        env: process.env.NODE_ENV,
      },
      query: userMessage,
    };

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时

    try {
      const response = await fetch('/api/dify/workflows/run/fault_report_analysis/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'text/event-stream',
          token: 'LLM',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API错误响应:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      // 检查Content-Type是否为SSE格式
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('text/event-stream')) {
        // 处理非SSE响应
        const responseText = await response.text();
        try {
          const jsonResponse = JSON.parse(responseText);
          if (jsonResponse.answer) {
            setBotMessageContent(botMessage, jsonResponse.answer);
          } else {
            setBotMessageContent(botMessage, '抱歉，我暂时无法回答您的问题，请稍后再试。');
          }
        } catch (parseError) {
          setBotMessageContent(botMessage, '抱歉，服务响应格式异常，请稍后再试。');
        }
        return;
      }

      // 处理SSE流
      await processSSEStream(response, botMessage);
    } catch (error: any) {
      if (error.name === 'AbortError') {
        setBotMessageContent(botMessage, '请求超时，请稍后再试。');
      } else {
        throw error;
      }
    }
  }

  // 尝试从截断的JSON中提取答案
  function tryExtractAnswerFromTruncatedJSON(data: string): string | null {
    try {
      // 查找answer字段的模式
      const answerMatch = data.match(/"answer":\s*"([^"]*(?:\\.[^"]*)*)/);
      if (answerMatch && answerMatch[1]) {
        // 处理转义字符
        return answerMatch[1].replace(/\\"/g, '"').replace(/\\n/g, '\n').replace(/\\\\/g, '\\');
      }

      // 查找outputs.answer的模式
      const outputsAnswerMatch = data.match(/"outputs":\s*{[^}]*"answer":\s*"([^"]*(?:\\.[^"]*)*)/);
      if (outputsAnswerMatch && outputsAnswerMatch[1]) {
        return outputsAnswerMatch[1]
          .replace(/\\"/g, '"')
          .replace(/\\n/g, '\n')
          .replace(/\\\\/g, '\\');
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  // 处理SSE流
  async function processSSEStream(response: Response, botMessage: Message) {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法读取响应流');
    }

    const decoder = new TextDecoder();
    let hasReceivedContent = false;
    let buffer = ''; // 用于缓存不完整的数据

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 按行分割，但保留最后一行（可能不完整）
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留最后一行作为缓冲

        for (const line of lines) {
          if (line.trim() === '') continue;

          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();

            if (data === '[DONE]' || data === 'data: [DONE]') {
              return;
            }

            if (data === '') continue;

            try {
              // 尝试解析JSON
              const parsed = JSON.parse(data);
              const actuallyAddedContent = await handleDifyWorkflowEvent(parsed, botMessage);
              if (actuallyAddedContent) {
                hasReceivedContent = true;
              }
            } catch (parseError) {
              // 尝试从截断的JSON中提取答案
              const extractedAnswer = tryExtractAnswerFromTruncatedJSON(data);
              if (extractedAnswer) {
                setBotMessageContent(botMessage, extractedAnswer);
                hasReceivedContent = true;
              }
              // 静默处理JSON解析错误
              continue;
            }
          }
        }
      }

      // 处理缓冲区中剩余的数据
      if (buffer.trim() && buffer.startsWith('data: ')) {
        const data = buffer.slice(6).trim();
        if (data && data !== '[DONE]') {
          try {
            const parsed = JSON.parse(data);
            const actuallyAddedContent = await handleDifyWorkflowEvent(parsed, botMessage);
            if (actuallyAddedContent) {
              hasReceivedContent = true;
            }
          } catch (parseError) {
            // 静默处理缓冲区解析错误
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // 检查是否收到内容
    if (!hasReceivedContent) {
      setBotMessageContent(botMessage, '抱歉，我暂时无法回答您的问题，请稍后再试。');
    }
  }

  // 处理Dify工作流事件
  async function handleDifyWorkflowEvent(event: any, botMessage: Message): Promise<boolean> {
    let contentAdded = false;

    switch (event.event) {
      case 'workflow_started':
      case 'node_started':
      case 'workflow_finished':
        // 不显示执行进度
        break;

      case 'node_finished':
        // 检查是否是answer节点，如果是则提取答案
        if (event.data?.node_type === 'answer' && event.data?.outputs?.answer) {
          const answerContent = event.data.outputs.answer;
          if (answerContent && typeof answerContent === 'string') {
            setBotMessageContent(botMessage, answerContent);
            contentAdded = true;
          }
        }
        break;

      case 'text_chunk':
        const streamContent = event.data?.text;
        if (streamContent && typeof streamContent === 'string') {
          updateBotMessageContent(botMessage, streamContent);
          contentAdded = true;
        }
        break;

      case 'message':
        // 处理message事件，提取answer内容
        const messageContent = event.data?.answer || event.answer;
        if (messageContent && typeof messageContent === 'string') {
          updateBotMessageContent(botMessage, messageContent);
          contentAdded = true;
        }
        break;

      case 'message_end':
        // 从message_end事件中提取最终答案（通常答案在node_finished中，这里作为备用）
        const finalAnswer =
          event.data?.answer ||
          event.answer ||
          event.data?.content ||
          event.content ||
          event.data?.text ||
          event.text;

        if (finalAnswer && typeof finalAnswer === 'string') {
          setBotMessageContent(botMessage, finalAnswer);
          contentAdded = true;
        }
        break;

      case 'error':
        const errorMsg = event.data?.message || event.data?.error || '工作流执行出错';
        updateBotMessageContent(botMessage, `\n错误: ${errorMsg}\n`);
        contentAdded = true;
        break;

      default:
        // 静默处理未知事件类型
        break;
    }

    return contentAdded;
  }

  // 设置机器人消息内容（替换模式）
  function setBotMessageContent(botMessage: Message, content: string) {
    // 找到最后一条机器人消息（应该就是当前正在更新的消息）
    const messageIndex = messages.value.length - 1;

    if (messageIndex >= 0 && messages.value[messageIndex].type === 'bot') {
      const currentMessage = messages.value[messageIndex];

      // 如果已经接收到内容，则不再替换，避免重复
      if (currentMessage.hasReceivedContent) {
        return;
      }

      // 创建新的消息对象以确保响应式更新
      const updatedMessage = {
        ...currentMessage,
        content: content,
        loading: false, // 移除 loading 状态
        hasReceivedContent: true, // 标记已经接收到实际内容
      };

      // 使用splice替换消息以确保响应式更新
      messages.value.splice(messageIndex, 1, updatedMessage);

      // 强制触发响应式更新
      nextTick();
    }
  }

  // 更新机器人消息内容（智能模式：首次替换，后续追加）
  function updateBotMessageContent(_botMessage: Message, newContent: string) {
    // 找到最后一条机器人消息（应该就是当前正在更新的消息）
    const messageIndex = messages.value.length - 1;

    if (messageIndex >= 0 && messages.value[messageIndex].type === 'bot') {
      const currentMessage = messages.value[messageIndex];

      // 如果当前消息是"正在思考"状态，则替换为新内容
      const isThinking =
        currentMessage.content.includes('正在分析故障') ||
        currentMessage.content.includes('正在思考') ||
        currentMessage.content.includes('请稍候');

      let finalContent: string;
      if (isThinking) {
        // 首次接收内容，替换思考文字
        finalContent = newContent;
      } else if (currentMessage.hasReceivedContent) {
        // 已经有内容，追加新内容（用于流式更新）
        finalContent = currentMessage.content + newContent;
      } else {
        // 其他情况，直接设置内容
        finalContent = newContent;
      }

      const updatedMessage = {
        ...currentMessage,
        content: finalContent,
        loading: false, // 移除 loading 状态
        hasReceivedContent: true, // 标记已经接收到实际内容
      };

      // 使用splice替换消息以确保响应式更新
      messages.value.splice(messageIndex, 1, updatedMessage);

      // 强制触发响应式更新
      nextTick();
    }
  }

  // 初始化
  initWelcomeMessage();

  // 暴露方法给父组件
  defineExpose({
    resetSession,
    messages,
  });
</script>

<style lang="less" scoped>
  .tab-chat-container {
    .chat-content {
      background-color: #fafafa;
    }

    :deep(.markdown-content) {
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
      }

      p {
        margin-bottom: 0.5em;
      }

      pre {
        padding: 16px;
        overflow-x: auto;
        border-radius: 6px;
        background-color: #f6f8fa;
      }

      code {
        padding: 2px 4px;
        border-radius: 3px;
        background-color: #f6f8fa;
        font-size: 0.9em;
      }

      pre code {
        padding: 0;
        background-color: transparent;
      }
    }
  }
</style>
