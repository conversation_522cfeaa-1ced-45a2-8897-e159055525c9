<template>
  <div class="p-4">
    <h1>Markdown表格渲染测试</h1>
    
    <div class="mb-4">
      <a-button @click="testSimpleTable" class="mr-2">测试简单表格</a-button>
      <a-button @click="testComplexTable" class="mr-2">测试复杂表格</a-button>
      <a-button @click="testMixedContent" class="mr-2">测试混合内容</a-button>
      <a-button @click="resetChat" type="primary">重置聊天</a-button>
    </div>

    <div class="chat-test-container">
      <FaultAnalysisChat
        ref="chatRef"
        :fault-id="'test-fault-001'"
        :fault-data="[{ fid: 'test-fault-001', faultDescribe: '表格渲染测试故障' }]"
        mode="inline"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import FaultAnalysisChat from '@/components/FaultAnalysisChat/index.vue';

const chatRef = ref();

// 简单表格测试
const simpleTableMarkdown = `
# 简单表格测试

这是一个简单的表格：

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |
| 数据7 | 数据8 | 数据9 |

表格渲染完成。
`;

// 复杂表格测试
const complexTableMarkdown = `
# 复杂表格测试

## 故障统计表

| 故障ID | 故障类型 | 严重程度 | 发生时间 | 状态 | 负责人 |
|--------|----------|----------|----------|------|--------|
| F001 | 网络连接异常 | 高 | 2024-01-15 10:30 | 已修复 | 张三 |
| F002 | 数据库连接超时 | 中 | 2024-01-15 11:45 | 处理中 | 李四 |
| F003 | 服务器内存不足 | 高 | 2024-01-15 12:20 | 待处理 | 王五 |
| F004 | API响应缓慢 | 低 | 2024-01-15 13:10 | 已修复 | 赵六 |

## 性能指标表

| 指标名称 | 当前值 | 阈值 | 状态 | 趋势 |
|----------|--------|------|------|------|
| CPU使用率 | 75% | 80% | 正常 | ↗️ |
| 内存使用率 | 85% | 90% | 警告 | ↗️ |
| 磁盘使用率 | 60% | 85% | 正常 | → |
| 网络延迟 | 120ms | 200ms | 正常 | ↘️ |

表格包含了多种数据类型和格式。
`;

// 混合内容测试
const mixedContentMarkdown = `
# 混合内容测试

## 1. 故障分析报告

### 基本信息
- **故障ID**: F001
- **发生时间**: 2024-01-15 10:30:00
- **影响范围**: 全系统

### 详细数据

| 时间段 | 错误数量 | 成功率 | 平均响应时间 |
|--------|----------|--------|--------------|
| 10:00-10:30 | 0 | 100% | 150ms |
| 10:30-11:00 | 25 | 85% | 800ms |
| 11:00-11:30 | 12 | 92% | 400ms |
| 11:30-12:00 | 3 | 98% | 200ms |

### 代码示例

\`\`\`javascript
// 错误处理代码
function handleError(error) {
  console.error('故障发生:', error);
  return {
    status: 'error',
    message: error.message
  };
}
\`\`\`

### 解决方案对比

| 方案 | 实施难度 | 预计时间 | 成本 | 风险等级 |
|------|----------|----------|------|----------|
| 方案A | 低 | 2小时 | 低 | 低 |
| 方案B | 中 | 1天 | 中 | 中 |
| 方案C | 高 | 3天 | 高 | 低 |

> **建议**: 优先考虑方案A，快速解决问题。

### 总结
1. 故障已定位
2. 解决方案已确定
3. 预计2小时内修复完成
`;

function testSimpleTable() {
  simulateMessage(simpleTableMarkdown);
}

function testComplexTable() {
  simulateMessage(complexTableMarkdown);
}

function testMixedContent() {
  simulateMessage(mixedContentMarkdown);
}

function resetChat() {
  chatRef.value?.resetSession();
}

// 模拟消息发送
function simulateMessage(content: string) {
  if (chatRef.value) {
    // 直接添加机器人消息来测试渲染
    chatRef.value.messages.push({
      type: 'bot',
      content: content,
      timestamp: Date.now(),
    });
  }
}
</script>

<style lang="less" scoped>
.chat-test-container {
  height: 600px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
}
</style>
