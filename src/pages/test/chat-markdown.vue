<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">聊天Markdown测试页面</h1>

    <div class="mb-4">
      <a-button @click="addMarkdownMessage" type="primary">添加Markdown测试消息</a-button>
      <a-button @click="clearMessages" class="ml-2">清空消息</a-button>
    </div>

    <!-- 聊天组件 -->
    <FaultAnalysisChat
      :faultId="'test-fault-id'"
      :faultData="{ faultDescribe: '测试故障描述' }"
      :position="{ bottom: '2rem', right: '2rem' }"
    />

    <!-- 使用说明 -->
    <div class="mt-8">
      <h2 class="text-xl font-semibold mb-4">使用说明</h2>
      <div class="border p-4 rounded bg-blue-50">
        <h3 class="font-semibold mb-2">如何测试Markdown功能：</h3>
        <ol class="list-decimal list-inside space-y-1">
          <li>点击右下角的蓝色聊天气泡打开聊天窗口</li>
          <li>在输入框中输入包含markdown格式的文本</li>
          <li>发送消息后，机器人回复的内容将自动渲染markdown格式</li>
          <li>支持的markdown格式包括：标题、粗体、斜体、代码块、列表、表格、链接等</li>
        </ol>

        <h3 class="font-semibold mt-4 mb-2">测试用的Markdown文本示例：</h3>
        <div class="bg-gray-100 p-2 rounded text-sm font-mono">
          # 故障分析请求<br />
          **问题描述**: 系统响应缓慢<br />
          `错误代码: 500`<br />
          - 检查服务器状态<br />
          - 查看日志文件<br />
          <br />
          请分析这个问题的**根本原因**
        </div>

        <h3 class="font-semibold mt-4 mb-2">功能特点：</h3>
        <ul class="list-disc list-inside space-y-1">
          <li>✅ 智能检测markdown格式，简单文本直接显示</li>
          <li>✅ 用户消息支持markdown格式（蓝色背景，白色文字）</li>
          <li>✅ 机器人回复支持markdown格式（灰色背景，黑色文字）</li>
          <li>✅ 支持代码高亮、表格、列表等丰富格式</li>
          <li>✅ 自动滚动到最新消息</li>
          <li>✅ 更大的聊天窗口（500x650px）</li>
          <li>✅ 更小的字体（12px主体，10px时间戳，0.8em代码）</li>
          <li>✅ 优化空白间隙，避免不必要的空白区域</li>
          <li>✅ 响应式设计，适配不同屏幕尺寸</li>
        </ul>

        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <h4 class="font-semibold text-yellow-800 mb-2">🔧 最新优化：</h4>
          <p class="text-yellow-700 text-sm">
            修复了聊天内容中出现大量空白区域的问题，并同步了markdown组件的字体样式：
          </p>
          <ul class="text-yellow-700 text-sm mt-2 list-disc list-inside">
            <li>简单文本直接显示，避免不必要的空白</li>
            <li>包含markdown语法的内容使用专业渲染器</li>
            <li>大幅减少了消息间的空隙</li>
            <li>统一了markdown内容的字体大小和间距</li>
            <li>优化了标题、段落、列表、代码、表格等元素的样式</li>
          </ul>
        </div>

        <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded">
          <h4 class="font-semibold text-green-800 mb-2">📏 字体规格：</h4>
          <ul class="text-green-700 text-sm list-disc list-inside">
            <li><strong>主体文字</strong>: 12px (标题、段落、列表)</li>
            <li><strong>代码块</strong>: 10px (行内代码、代码块、表格)</li>
            <li><strong>时间戳</strong>: 10px</li>
            <li><strong>行高</strong>: 1.4 (紧凑布局)</li>
            <li><strong>间距</strong>: 减小了各元素间的margin和padding</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 测试用的markdown内容展示 -->
    <div class="mt-8">
      <h2 class="text-xl font-semibold mb-4">Markdown内容预览</h2>
      <div class="border p-4 rounded bg-gray-50">
        <MarkdownViewer :value="testMarkdown" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import FaultAnalysisChat from '@/components/FaultAnalysisChat/index.vue';
  import { MarkdownViewer } from '@/components/Markdown';

  const testMarkdown = ref(`
# 故障分析报告

## 问题概述
这是一个**重要**的故障分析报告，包含以下内容：

### 主要问题
1. **系统响应缓慢**
   - 数据库查询超时
   - 内存使用率过高
2. **网络连接异常**
   - 丢包率达到 \`5%\`
   - 延迟超过 \`200ms\`

### 解决方案
\`\`\`bash
# 重启服务
systemctl restart nginx
systemctl restart mysql

# 检查日志
tail -f /var/log/nginx/error.log
\`\`\`

### 监控数据
| 指标 | 当前值 | 正常范围 | 状态 |
|------|--------|----------|------|
| CPU使用率 | 85% | < 70% | ⚠️ 警告 |
| 内存使用率 | 92% | < 80% | 🔴 异常 |
| 磁盘使用率 | 45% | < 90% | ✅ 正常 |

> **注意**: 需要立即处理内存使用率过高的问题

### 相关链接
- [系统监控面板](https://monitor.example.com)
- [故障处理手册](https://docs.example.com/troubleshooting)

---

**报告生成时间**: 2024-01-15 14:30:00
`);

  function addMarkdownMessage() {
    // 这个函数用于测试，实际使用时聊天组件会自动处理markdown
    console.log('添加Markdown测试消息');
    // 注意：实际的消息添加需要通过聊天组件的API接口
    alert('请点击聊天气泡打开聊天窗口，然后发送消息来测试markdown功能');
  }

  function clearMessages() {
    console.log('清空消息');
    // 注意：实际的消息清空需要通过聊天组件的重置功能
    alert('请在聊天窗口中点击"重置"按钮来清空消息');
  }
</script>

<style scoped>
  /* 测试页面样式 */
</style>
