<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">故障分析聊天组件测试</h1>

    <div class="mb-4">
      <h2 class="text-lg font-semibold mb-2">测试说明</h2>
      <p class="text-gray-600 mb-2">
        这是故障分析聊天组件的测试页面。聊天气泡应该出现在页面右下角。
        点击气泡可以打开聊天窗口，输入问题后会调用故障分析API。
      </p>
      <div class="bg-blue-50 p-3 rounded">
        <h3 class="font-medium text-blue-800 mb-1">SSE流式响应支持</h3>
        <p class="text-blue-700 text-sm">
          现在支持Server-Sent Events (SSE)流式响应，AI回复会实时显示，提供更好的用户体验。
        </p>
      </div>
    </div>

    <div class="mb-4">
      <h2 class="text-lg font-semibold mb-2">模拟故障数据</h2>
      <div class="bg-gray-100 p-4 rounded">
        <p><strong>故障ID:</strong> {{ mockFaultData.fid }}</p>
        <p><strong>故障描述:</strong> {{ mockFaultData.faultDescribe }}</p>
        <p><strong>开始时间:</strong> {{ mockFaultData.startTime }}</p>
        <p><strong>恢复时间:</strong> {{ mockFaultData.recoverTime }}</p>
      </div>
    </div>

    <!-- 故障分析聊天组件 -->
    <FaultAnalysisChat :fault-id="mockFaultData.fid" :fault-data="mockFaultData" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import FaultAnalysisChat from '@/components/FaultAnalysisChat/index.vue';

  // 模拟故障数据
  const mockFaultData = ref({
    fid: 'TEST-001',
    faultDescribe: '测试故障：数据库连接超时导致服务不可用',
    startTime: '2024-01-15 10:30:00',
    recoverTime: '2024-01-15 12:45:00',
    discoveryTime: '2024-01-15 10:35:00',
    responseTime: '2024-01-15 10:40:00',
    problem: '数据库连接池配置不当，在高并发情况下出现连接超时',
    stage: '已发布',
    driver: '张三',
    currentProgress: '已复盘',
  });
</script>

<style scoped>
  /* 测试页面样式 */
</style>
