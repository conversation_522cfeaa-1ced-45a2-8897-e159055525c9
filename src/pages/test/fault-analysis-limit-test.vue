<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">故障分析数量限制测试</h1>
    
    <div class="mb-4">
      <h2 class="text-lg font-semibold mb-2">测试场景</h2>
      <div class="space-y-2">
        <a-button @click="testSingleFault" type="primary">测试单个故障</a-button>
        <a-button @click="testMultipleFaults" type="primary">测试多个故障（5个）</a-button>
        <a-button @click="testExceedLimit" type="primary">测试超过限制（15个）</a-button>
        <a-button @click="testExactLimit" type="primary">测试恰好限制（10个）</a-button>
      </div>
    </div>

    <div class="mb-4">
      <h3 class="text-md font-semibold mb-2">当前测试：{{ currentTestName }}</h3>
      <p class="text-sm text-gray-600 mb-2">故障数量：{{ currentFaultCount }}</p>
    </div>

    <!-- 故障分析聊天组件 -->
    <div class="border rounded-lg" style="height: 600px;">
      <FaultAnalysisChat
        :fault-id="currentFaultIds"
        :fault-data="currentFaultData"
        mode="inline"
        ref="chatRef"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import FaultAnalysisChat from '@/components/FaultAnalysisChat/index.vue';

  const chatRef = ref();
  const currentTestName = ref('未开始测试');
  const currentFaultIds = ref('');
  const currentFaultData = ref([]);
  const currentFaultCount = ref(0);

  // 生成测试故障数据
  function generateTestFaultData(count: number) {
    const faultData = [];
    const faultIds = [];
    
    for (let i = 1; i <= count; i++) {
      const faultId = `test-fault-${i}`;
      faultIds.push(faultId);
      faultData.push({
        fid: faultId,
        faultDescribe: `测试故障 ${i} - 这是一个用于测试的故障描述`,
      });
    }
    
    return {
      faultIds: faultIds.join(','),
      faultData,
    };
  }

  // 测试单个故障
  function testSingleFault() {
    const { faultIds, faultData } = generateTestFaultData(1);
    currentTestName.value = '单个故障测试';
    currentFaultIds.value = faultIds;
    currentFaultData.value = faultData;
    currentFaultCount.value = 1;
    
    // 重置聊天会话
    setTimeout(() => {
      chatRef.value?.resetSession();
    }, 100);
  }

  // 测试多个故障（5个）
  function testMultipleFaults() {
    const { faultIds, faultData } = generateTestFaultData(5);
    currentTestName.value = '多个故障测试（5个）';
    currentFaultIds.value = faultIds;
    currentFaultData.value = faultData;
    currentFaultCount.value = 5;
    
    // 重置聊天会话
    setTimeout(() => {
      chatRef.value?.resetSession();
    }, 100);
  }

  // 测试超过限制（15个）
  function testExceedLimit() {
    const { faultIds, faultData } = generateTestFaultData(15);
    currentTestName.value = '超过限制测试（15个）';
    currentFaultIds.value = faultIds;
    currentFaultData.value = faultData;
    currentFaultCount.value = 15;
    
    // 重置聊天会话
    setTimeout(() => {
      chatRef.value?.resetSession();
    }, 100);
  }

  // 测试恰好限制（10个）
  function testExactLimit() {
    const { faultIds, faultData } = generateTestFaultData(10);
    currentTestName.value = '恰好限制测试（10个）';
    currentFaultIds.value = faultIds;
    currentFaultData.value = faultData;
    currentFaultCount.value = 10;
    
    // 重置聊天会话
    setTimeout(() => {
      chatRef.value?.resetSession();
    }, 100);
  }

  // 初始化为单个故障测试
  testSingleFault();
</script>

<style lang="less" scoped>
  .space-y-2 > * + * {
    margin-top: 0.5rem;
  }
</style>
