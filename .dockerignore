# 依赖目录
node_modules/

# 构建输出
dist/
dist.zip

# 开发工具
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Git
.git/
.gitignore

# Docker
Dockerfile*
.dockerignore

# 文档
README.md
CHANGELOG*.md
docs/

# 缓存
.eslintcache
.npm/
.cache/

# 临时文件
tmp/
temp/
