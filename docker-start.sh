#!/bin/bash

# ITMP Frontend Docker 启动脚本

echo "🚀 ITMP Frontend Docker 部署脚本"
echo "=================================="

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 显示选项菜单
echo "请选择部署模式："
echo "1) 开发环境 (端口: 9999, 支持热重载)"
echo "2) 生产环境 (端口: 3000, 静态文件服务)"
echo "3) 停止所有服务"
echo "4) 查看服务状态"
echo "5) 查看日志"

read -p "请输入选项 (1-5): " choice

case $choice in
    1)
        echo "🔧 启动开发环境..."
        docker-compose up -d itmp-frontend-dev
        echo "✅ 开发环境已启动，访问地址: http://localhost:9999"
        ;;
    2)
        echo "🏭 启动生产环境..."
        docker-compose --profile production up -d itmp-frontend-prod
        echo "✅ 生产环境已启动，访问地址: http://localhost:3000"
        ;;
    3)
        echo "🛑 停止所有服务..."
        docker-compose down
        echo "✅ 所有服务已停止"
        ;;
    4)
        echo "📊 服务状态："
        docker-compose ps
        ;;
    5)
        echo "📋 选择要查看日志的服务："
        echo "1) 开发环境日志"
        echo "2) 生产环境日志"
        read -p "请输入选项 (1-2): " log_choice
        case $log_choice in
            1)
                docker-compose logs -f itmp-frontend-dev
                ;;
            2)
                docker-compose logs -f itmp-frontend-prod
                ;;
            *)
                echo "❌ 无效选项"
                ;;
        esac
        ;;
    *)
        echo "❌ 无效选项，请重新运行脚本"
        exit 1
        ;;
esac
