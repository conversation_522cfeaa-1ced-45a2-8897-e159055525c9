# 调试版 Dockerfile - 更详细的日志输出
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=development
ENV NODE_OPTIONS=--max-old-space-size=8192
ENV DEBUG=vite:*

# 设置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm
RUN npm install -g pnpm@8.10.0

# 复制 package.json 和相关配置文件
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml ./
COPY packages/ ./packages/
COPY internal/ ./internal/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 9999

# 启动命令 - 详细模式
CMD ["sh", "-c", "echo '🚀 启动 Vite 开发服务器...' && pnpm dev --host 0.0.0.0 --port 9999 --debug"]
