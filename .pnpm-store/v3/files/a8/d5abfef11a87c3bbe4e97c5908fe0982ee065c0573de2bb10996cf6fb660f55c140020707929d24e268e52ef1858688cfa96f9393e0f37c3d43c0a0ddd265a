{"name": "@commitlint/config-conventional", "version": "18.6.3", "description": "Shareable commitlint config enforcing conventional commits", "main": "lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "import": "./wrapper.mjs", "require": "./lib/index.js"}}, "files": ["lib/", "wrapper.mjs"], "scripts": {"deps": "dep-check", "pkg": "pkg-check"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/config-conventional"}, "keywords": ["conventional-changelog", "commitlint", "commitlint-config", "angular"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "engines": {"node": ">=v18"}, "devDependencies": {"@commitlint/lint": "^18.6.1", "@commitlint/utils": "^18.6.1"}, "dependencies": {"@commitlint/types": "^18.6.1", "conventional-changelog-conventionalcommits": "^7.0.2"}, "gitHead": "5bf618c7fea9f0b00c4a8546dece834b4a2335d3"}