// Type definitions for Helpers submodule of UAParser.js v2.0.3
// Project: https://github.com/faisalman/ua-parser-js
// Definitions by: <PERSON><PERSON><PERSON> <https://github.com/faisalman>

import type { UAParserExt } from "../main/ua-parser";

export const Bots: UAParserExt;
export const CLIs: UAParserExt;
export const Crawlers: UAParserExt;
export const ExtraDevices: UAParserExt;
export const Emails: UAParserExt;
export const Fetchers: UAParserExt;
export const InApps: UAParserExt;
export const Libraries: UAParserExt;
export const MediaPlayers: UAParserExt;
export const Vehicles: UAParserExt;