{"name": "cz-git", "version": "1.11.1", "description": "A better customizable and git support commitizen adapter", "author": "Zhengqbbb <<EMAIL>> (https://github.com/Zhengqbbb)", "license": "MIT", "homepage": "https://cz-git.qbb.sh", "repository": {"type": "git", "url": "https://github.com/Zhengqbbb/cz-git", "directory": "packages/cz-git"}, "bugs": {"url": "https://github.com/Zhengqbbb/cz-git/issues"}, "keywords": ["openai", "commit", "commitizen-adapter", "cli", "cz-cli", "cz-git", "cz-gitee", "cz-adapter", "customizable", "cz-customizable"], "sideEffects": false, "exports": {".": {"types": "./lib/index.d.ts", "import": "./lib/index.mjs", "require": "./lib/index.js"}, "./package.json": "./package.json"}, "main": "lib/index.js", "types": "lib/index.d.ts", "typesVersions": {"*": {"*": ["lib/index.d.ts"]}}, "files": ["lib"], "engines": {"node": ">=v12.20.0"}, "devDependencies": {"https-proxy-agent": "5.0.1", "node-fetch-cjs": "3.3.2", "rimraf": "3.0.2", "@cz-git/inquirer": "1.11.1", "@cz-git/loader": "1.11.1"}, "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> lib", "release:changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}}