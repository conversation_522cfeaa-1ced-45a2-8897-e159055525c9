/* tslint:disable */
/* eslint-disable */
/**
* @param {string} contents
* @returns {any}
*/
export function parse_to_json(contents: string): any;
/**
* @returns {string}
*/
export function version(): string;
/**
* @param {Uint8Array} contents
* @returns {Uint32Array}
*/
export function parse_to_uint32array(contents: Uint8Array): Uint32Array;
/**
*/
export enum TokenTypes {
  Key,
  Value,
  Section,
  CommentIndicator,
  CommentValue,
}
